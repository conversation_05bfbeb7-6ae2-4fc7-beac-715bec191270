# Scrapy settings for jd_detail project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html

BOT_NAME = "jd_detail"

SPIDER_MODULES = ["jd_detail.spiders"]
NEWSPIDER_MODULE = "jd_detail.spiders"
LOG_LEVEL = "WARNING"
HTTPERROR_ALLOWED_CODES = [301, 302,400,403, 404, 500, 502, 503, 504]
#配置Mysql
Mysql={
   'host':'rm-bp1482q53zdx93dg13o.mysql.rds.aliyuncs.com',
   'port':3306,
   'user':'aizinger_test',
   'password':'BA40Ry/lUGicHeNYnDnb391xEm+52SbFYoCqyjo9Y6vA',
   'database':'aizinger_test',
   'charset':'utf8mb4'
}
#加密解密密钥
SECRET_KEY = 'u2EGtC12k5iT43uM'
# Crawl responsibly by identifying yourself (and your website) on the user-agent
#USER_AGENT = "jd_detail (+http://www.yourdomain.com)"

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# Configure maximum concurrent requests performed by Scrapy (default: 16)
CONCURRENT_REQUESTS = 1

# DOWNLOADER_CLIENT_TLS_METHOD = "TLS"   # TLS连接
# DOWNLOADER_CLIENTCONTEXTFACTORY = "scrapy.core.downloader.contextfactory.ScrapyClientContextFactory"
# DOWNLOADER_CLIENT_ASYNC = True        # 启用异步

# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
# DOWNLOAD_DELAY = 3
# RANDOMIZE_DOWNLOAD_DELAY = True

# The download delay setting will honor only one of:
# CONCURRENT_REQUESTS_PER_DOMAIN = 16
#CONCURRENT_REQUESTS_PER_IP = 16

# Disable cookies (enabled by default)
#COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
#TELNETCONSOLE_ENABLED = False

# Override the default request headers:
DEFAULT_REQUEST_HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
}

# Enable or disable spider middlewares
# See https://docs.scrapy.org/en/latest/topics/spider-middleware.html
#SPIDER_MIDDLEWARES = {
#    "jd_detail.middlewares.JdDetailSpiderMiddleware": 543,
#}

# Enable or disable downloader middlewares
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
DOWNLOADER_MIDDLEWARES = {
    # 添加下面语句，将默认设置取消
    "jd_detail.downloadermiddlewares.useragent.UserAgentMiddleware": None,
   "jd_detail.middlewares.JdDetailDownloaderMiddleware": 543,#selenium+获取cookies
    # 'jd_detail.middlewares.ProxyMiddleware': 100,#代理IP
}
# 设置RANDOM_UA_TYPE类型
# https://github.com/hellysmile/fake-useragent
RANDOM_UA_TYPE = "chrome"
# Enable or disable extensions
# See https://docs.scrapy.org/en/latest/topics/extensions.html
#EXTENSIONS = {
#    "scrapy.extensions.telnet.TelnetConsole": None,
#}

# Configure item pipelines
# See https://docs.scrapy.org/en/latest/topics/item-pipeline.html

ITEM_PIPELINES = {
   # "jd_detail.pipelines.JdDetailPipeline": 300,
   #  "jd_detail.pipelines.CsvPipeline": 300,
    "jd_detail.pipelines.JdMySQLPipeline": 300,
}
# Enable and configure the AutoThrottle extension (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
#AUTOTHROTTLE_ENABLED = True
# The initial download delay
#AUTOTHROTTLE_START_DELAY = 5
# The maximum download delay to be set in case of high latencies
#AUTOTHROTTLE_MAX_DELAY = 60
# The average number of requests Scrapy should be sending in parallel to
# each remote server
#AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
# Enable showing throttling stats for every response received:
#AUTOTHROTTLE_DEBUG = False

# Enable and configure HTTP caching (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
#HTTPCACHE_ENABLED = True
#HTTPCACHE_EXPIRATION_SECS = 0
#HTTPCACHE_DIR = "httpcache"
#HTTPCACHE_IGNORE_HTTP_CODES = []
#HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"

# Set settings whose default value is deprecated to a future-proof value
REQUEST_FINGERPRINTER_IMPLEMENTATION = "2.7"
TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
FEED_EXPORT_ENCODING = "utf-8"

# Retry settings
# RETRY_ENABLED = True
# RETRY_TIMES = 5  # 想重试几次就写几


BAIDU_OCR_API_KEY = "Z/Bpx0hPbE1pDL239NV5isLruVZEOOTibwJh6B3IE67MWNCdZaN2nw=="
BAIDU_OCR_SECRET_KEY = "USG3vGCW7RQAJ6Dh44HsWdqB8h9kRp2bRXQheOEvnxVAMBw6SE/xmjsQiqCFRTp4"
import pymysql
from settings import config


def db_init():
    conn = pymysql.connect(
        host=config.get('database').get("host"),
        port=config.get('database').get("port"),
        user=config.get('database').get("user"),
        password=config.get('database').get("password"),
        database=config.get('database').get("database")
        )

    c = conn.cursor(pymysql.cursors.DictCursor)

    for i in range(20):
        sql = 'select * from 20220317kocmobile where partition_code=%s'
        c.execute(sql, i)
        data = c.fetchall()
        if not data:
            break
        file_num = '%02d' % i
        file_name = "koc-pro-%s.vcf" % file_num
        print(file_name)
        with open(file_name, 'w', encoding='utf8') as fw:
            count = 0
            for line in data:
                content = ["BEGIN:VCARD", "VERSION:2.1", "", "", "", "END:VCARD\n\n", ]
                mobile = line.get("receiver_mobile")
                if len(mobile) != 11:
                    continue
                content[2] = "N:" + "bb%s-%s" % (file_num, mobile)
                content[3] = "FN:" + "bb%s-%s" % (file_num, mobile)
                content[4] = "TEL;CELL;VOICE:" + mobile
                str = "\n".join(content)
                fw.write(str)
                count += 1
                # if count > 200:
                #     break
        print(f"{count}条数据写入完成")
    c.close()
    conn.close()


if __name__ == '__main__':
    db_init()
import datetime
import json
import random
import time
import traceback
import platform
import urllib3
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.common.keys import Keys
from lxml import etree
import __init__
from dateutil.parser import parse
from retrying import retry

from utils.logx import logger
from tm_comment.database import db, rds
from settings import config


def random_sleep(min_v=1, max_v=3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


class TmClientSpider:
    def __init__(self):
        chrome_options = webdriver.ChromeOptions()
        # 以下这些都是为了防止浏览器崩溃，而增加的一些属性
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        chrome_options.add_argument('--no-sandbox')  # 禁用沙盒，防止浏览器崩溃
        chrome_options.add_argument('--disable-dev-shm-usage')  # 不知道什么意思，也是防崩溃的
        chrome_options.add_argument(
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36")
        #
        # chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_argument("--disable-blink-features")
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.browser = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.browser, 20)  # 设置等待时常，如果在指定时间内未获取到元素，则报错
        self.url = "https://login.taobao.com/member/login.jhtml"
        # 在登录时赋值
        self.username = config.get("tb_account").get("username")
        self.password = config.get("tb_account").get("password")

    def complete(self, time_out) -> bool:
        end_time = time.time() + time_out
        while True:
            if self.browser.current_url == 'https://myseller.taobao.com/home.htm#/index':
                return True
            time.sleep(1)
            now = time.time()
            if now > end_time:
                break
        return False

    # 淘宝登录
    def account_login(self):
        # 1. 请求网址
        self.browser.get(self.url)
        # 增加请求头，用户躲避滑动验证
        random_sleep(3, 5)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        # 输入账号
        username_inp = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'fm-login-id'))
        )
        for i in self.username:
            username_inp.send_keys(i)
            random_sleep(0.3, 1.5)
        logger.info('账号已输入')
        random_sleep()
        # 输入密码
        password_inp = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//*[@id="fm-login-password"]'))
        )
        for i in self.password:
            password_inp.send_keys(i)
            random_sleep(0.3, 1.5)
        logger.info('密码已输入')
        random_sleep()

        # 登录框
        submit_but = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//button[@type="submit"]'))
        )
        submit_but.click()
        logger.info('点击登录按钮')
        if self.complete(20):
            logger.info('登录成功')
            self.cookie = self.browser.get_cookies()
            self.save_cookie()
            self.jk_crm_login()
            logger.info('集客登录成功')
            return True
        if self.browser.current_url.startswith('https://login.taobao.com/member/login_unusual.htm'):
            if self.send_code():
                logger.info('登录成功')
                self.cookie = self.browser.get_cookies()
                self.save_cookie()
                self.jk_crm_login()
                logger.info('集客登录成功')
                return True
        logger.error("登录失败")
        return

    def send_code(self):
        self.browser.switch_to.frame(0)
        for i in range(2):
            # 获取验证码
            get_code_but = self.wait.until(
                Ec.element_to_be_clickable((By.ID, 'J_GetCode'))
            )
            get_code_but.click()
            logger.info('获取验证码')
            # 验证码有效期15分钟
            for i in range(3):
                time.sleep(60*3)
                sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel in ('%s', '%s') order by insert_time desc limit 1" % (time.strftime(
                                    "%Y-%m-%d", time.localtime()), "tm", 'test')
                cur = db.execute(sql)
                row = cur.fetchone()
                if not row:
                    logger.info("数据未查到")
                    continue
                res = row[0]
                send_code_inp = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="J_Phone_Checkcode"]'))
                )
                send_code_inp.send_keys(res)
                logger.info("%s验证码已输入" % res)
                time.sleep(1)

                submit_btn = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="submitBtn"]'))
                )
                submit_btn.click()
                logger.info("验证码已提交")
                time.sleep(3)

                if self.complete(10):
                    return True

    def save_cookie(self):
        cookie = {}
        for i in self.cookie:
            cookie[i.get("name")] = i.get("value")
        cookie_str = json.dumps(cookie)
        rds.hset(f'tb_{self.username}', 'cookie', cookie_str)

    def jk_crm_login(self):
        self.browser.get("https://fuwu.taobao.com/ser/my_service.htm?spm=a217wi.openworkbeanchtmall_web")
        random_sleep(3, 5)
        jk_button = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//img[@src="https://tida.alicdn.com/oss_1654743189476_1650579876_pvEW5pi2.png"]'))
        )
        jk_button.click()
        random_sleep(3, 5)
        self.browser.switch_to.window(self.browser.window_handles[-1])
        self.save_jk_cookie(self.browser.get_cookies())

    def save_jk_cookie(self, cookies):
        cookie_dict = {}
        for i in cookies:
            cookie_dict[i.get("name")] = i.get("value")
        cookie_str = json.dumps(cookie_dict)
        rds.hset(f'jk_crm_{self.username}', 'cookie', cookie_str)

    def get_jk_cookie(self):
        cookies = rds.hget(f"jk_crm_{self.username}", "cookie")
        if cookies:
            logger.info("get cookie is success")
            return json.loads(cookies)
        logger.info("get cookie is None")

    def sycm_login(self):
        self.browser.get("https://sycm.taobao.com/portal/home.htm?activeKey=operator&dateRange=2022-11-27%7C2022-11-27&dateType=day")
        random_sleep(3, 5)
        # jk_button = self.wait.until(
        #     Ec.element_to_be_clickable((By.XPATH, '//img[@src="https://tida.alicdn.com/oss_1654743189476_1650579876_pvEW5pi2.png"]'))
        # )
        # jk_button.click()
        # random_sleep(3, 5)
        self.browser.switch_to.window(self.browser.window_handles[-1])
        self.save_sycm_cookie(self.browser.get_cookies())

    def save_sycm_cookie(self, cookies):
        cookie_dict = {}
        for i in cookies:
            cookie_dict[i.get("name")] = i.get("value")
        cookie_str = json.dumps(cookie_dict)
        rds.hset(f'sycm_{self.username}', 'cookie', cookie_str)

    def get_sycm_cookie(self):
        cookies = rds.hget(f"sycm_{self.username}", "cookie")
        if cookies:
            logger.info("get cookie is success")
            return json.loads(cookies)
        logger.info("get cookie is None")


if __name__ == '__main__':
    TmClientSpider().account_login()
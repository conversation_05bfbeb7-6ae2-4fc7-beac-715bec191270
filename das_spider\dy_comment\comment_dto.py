import time


class CommentParam:
    def __init__(self, page=0, page_size=50, id='', rank='', filter='0', comment_time_from='',
                 comment_time_to=''):
        self.page = page
        self.page_size = page_size
        self.id = id
        self.rank = rank
        self.filter = filter
        self.comment_time_from = format_to_timestamp(comment_time_from)
        self.comment_time_to = format_to_timestamp(comment_time_to)


def format_to_timestamp(t) -> int:
    if not t:
        return 0
    if isinstance(t, str):
        if len(t) == 10:
            res = int(time.mktime(time.strptime(t, "%Y-%m-%d")))
        elif len(t) == 19:
            res = int(time.mktime(time.strptime(t, "%Y-%m-%d %H:%M:%S")))
        else:
            res = 0
        return res
    elif isinstance(t, int):
        return t
    return 0

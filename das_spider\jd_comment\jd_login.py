import base64
import os
import time
import re
import numpy as np
import ssl
from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.common.action_chains import ActionChains
import random
import cv2
from urllib import request
import datetime
import json
import urllib3
from logx import logger
from comment_model import db, JDComment
from settings import config


urllib3.disable_warnings()

ssl._create_default_https_context = ssl._create_unverified_context

def random_sleep(min_v=1, max_v=3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


# JdLogin 京东登陆
class JdLogin:
    def __init__(self):
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        chrome_options.add_argument('--no-sandbox')

        # chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-dev-shm-usage')
        self.browser = webdriver.Chrome(options=chrome_options)
        # self.browser = webdriver.Chrome()
        self.wait = WebDriverWait(self.browser, 20)
        self.url = 'https://passport.shop.jd.com/login/index.action'
        self.cookies = ''
        self.username = config.get("jd_account").get("username")
        self.password = config.get("jd_account").get("password")

    def recognition(self):
        big_img = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-bigimg"]/img'))
        )
        big_src = big_img.get_attribute("src")
        small_img = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-smallimg"]/img'))
        )
        small_src = small_img.get_attribute("src")

        big_base64 = base64.b64decode(re.findall(";base64,(.*)", big_src)[0])
        small_base64 = base64.b64decode(re.findall(";base64,(.*)", small_src)[0])
        captcha1_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "captcha1.png")
        captcha2_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "captcha2.png")
        r3_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "r3.jpg")
        r4_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "r4.jpg")

        with open(captcha1_path, "wb") as f:
            f.write(big_base64)
        with open(captcha2_path, "wb") as f:
            f.write(small_base64)
        time.sleep(1)

        cv2.imwrite(r3_path, cv2.imread(captcha1_path, 0))
        cv2.imwrite(r4_path, cv2.imread(captcha2_path, 0))
        cv2.imwrite(r4_path, abs(255 - cv2.cvtColor(cv2.imread(r4_path), cv2.COLOR_BGR2GRAY)))
        result = cv2.matchTemplate(cv2.imread(r4_path), cv2.imread(r3_path), cv2.TM_CCOEFF_NORMED)
        x, y = np.unravel_index(result.argmax(), result.shape)

        cv2.rectangle(cv2.imread(r3_path), (y + 20, x + 20), (y + 136 - 25, x + 136 - 25), (7, 249, 151), 2)
        return y

    def login(self):
        self.browser.get(self.url)  # 请求网址
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"
        self.browser.execute_script(js_str)
        print('执行js')

        self.browser.find_element(By.XPATH, '//*[@clstag="pageclick|keycount|index_login|2"]').click()
        self.browser.switch_to.frame(self.browser.find_element(By.XPATH, '//*[@id="loginFrame"]'))  # 下面的账号密码按钮在ifram里，所以需要进入ifram
        print("切换至密码登陆")
        time.sleep(1)
        # 输入账号
        username = self.wait.until(
            Ec.element_to_be_clickable((By.ID, "loginname"))
        )
        for i in self.username:
            username.send_keys(i)
            random_sleep(0.3, 1.5)
        time.sleep(1)
        # 输入密码
        password = self.wait.until(
            Ec.element_to_be_clickable((By.ID, "nloginpwd"))
        )
        for i in self.password:
            password.send_keys(i)
            random_sleep(0.3, 1.5)
        print('密码已输入')
        # time.sleep(10)

        # 登录框
        submit = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div[@class="item login-btn2013"]/input[@type="button"]'))
        )
        submit.click()
        print('点击登录按钮')
        time.sleep(2)
        # input("111")
        rt = 0
        if self.browser.current_url == self.url:
            while True:
                rt += 1
                time.sleep(1)
                logger.info("login ct= {}".format(rt))
                if self.move():
                    break
        self.get_cookies()

    def move(self):
        xx = self.recognition()
        distance = int(xx / 1.285)
        # distance = int(xx * 281 / 360)  # 计算缩放比(281网页中图片的宽度，360背景图的实际宽度)

        # 轨迹
        tracks = self._get_tracks(distance)

        # 移动滑块
        self._slider_action(tracks)

        time.sleep(1)
        if self.browser.current_url == self.url:
            return False
        elif self.browser.current_url == 'https://shop.jd.com/':
            return True
        elif self.browser.current_url.startswith('https://aq.jd.com/certified/index'):
            # todo
            # self.send_captcha()
            exit(2)
            return True
        else:
            logger.error('move code')
            exit(1)

    def send_captcha(self):
        sms_mode = self.wait.until(
            Ec.element_to_be_clickable((By.CLASS_NAME, "mb20"))
        )
        sms_mode.click()
        time.sleep(1)

        for i in range(2):
            send_code = self.wait.until(
                Ec.element_to_be_clickable((By.CLASS_NAME, "btn-l"))
            )
            send_code.click()
            logger.info("第%s条短信已发送" % i)
            for i in range(2):
                time.sleep(60*2)
                sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel = '%s' order by insert_time desc limit 1" % (time.strftime(
                                    "%Y-%m-%d", time.localtime()), "jd")
                cur = db.execute(sql)
                row = cur.fetchone()
                if not row:
                    logger.info("数据未查到")
                    continue
                res = row[0]
                captcha_input = self.wait.until(
                    Ec.element_to_be_clickable((By.CLASS_NAME, "field"))
                )
                for i in res:
                    captcha_input.send_keys(i)
                    random_sleep(0.3, 1.5)
                logger.info("%s验证码已输入" % res)
                time.sleep(1)

                check_code_btn = self.wait.until(
                    Ec.element_to_be_clickable((By.CLASS_NAME, "btn-primary"))
                )
                check_code_btn.click()
                time.sleep(3)
                if self.browser.current_url == 'https://shop.jd.com/':
                    logger.info("登录成功")
                    return

            time.sleep(3)
            if self.browser.current_url == 'https://shop.jd.com/':
                logger.info("登录成功")
                return

    def get_random_float(self,min, max, digits=4):
        """
        :param min:
        :param max:
        :param digits:
        :return:
        """
        return round(random.uniform(min, max), digits)

    # 滑动轨迹
    def _get_tracks(self, distance):

        track = []
        mid1 = round(distance * random.uniform(0.1, 0.2))
        mid2 = round(distance * random.uniform(0.65, 0.76))
        mid3 = round(distance * random.uniform(0.84, 0.88))
        # 设置初始位置、初始速度、时间间隔
        current, v, t = 0, 0, 0.2
        distance = round(distance)

        while current < distance:
            # 四段加速度
            if current < mid1:
                a = random.randint(10, 15)
            elif current < mid2:
                a = random.randint(30, 40)
            elif current < mid3:
                a = -70
            else:
                a = random.randint(-25, -18)

            # 初速度 v0
            v0 = v
            # 当前速度 v = v0 + at
            v = v0 + a * t
            v = v if v >= 0 else 0
            move = v0 * t + 1 / 2 * a * (t ** 2)
            move = round(move if move >= 0 else 1)
            # 当前位移
            current += move
            # 加入轨迹
            track.append(move)

        # 超出范围
        back_tracks = []
        out_range = distance - current
        if out_range < -8:
            sub = int(out_range + 8)
            back_tracks = [-1, sub, -3, -1, -1, -1, -1]
        elif out_range < -2:
            sub = int(out_range + 3)
            back_tracks = [-1, -1, sub]

        return {'forward_tracks': track, 'back_tracks': back_tracks}

    # 开始滑动
    def _slider_action(self, tracks):

        # 点击滑块
        small_img = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-smallimg"]/img'))
        )
        ActionChains(self.browser).click_and_hold(on_element=small_img).perform()

        # 正向滑动
        for track in tracks['forward_tracks']:
            yoffset_random = random.uniform(-2, 4)
            ActionChains(self.browser).move_by_offset(xoffset=track, yoffset=yoffset_random).perform()

        time.sleep(random.uniform(0.06, 0.5))

        # 反向滑动
        for back_tracks in tracks['back_tracks']:
            yoffset_random = random.uniform(-2, 2)
            ActionChains(self.browser).move_by_offset(xoffset=back_tracks, yoffset=yoffset_random).perform()

        # 抖动
        ActionChains(self.browser).move_by_offset(
            xoffset=self.get_random_float(0, -1.67),
            yoffset=self.get_random_float(-1, 1)
        ).perform()
        ActionChains(self.browser).move_by_offset(
            xoffset=self.get_random_float(0, 1.67),
            yoffset=self.get_random_float(-1, 1)
        ).perform()

        time.sleep(self.get_random_float(0.6, 1))

        ActionChains(self.browser).release().perform()
        time.sleep(0.5)

    def get_pic(self):
        # 滑动拼图
        big_img = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-bigimg"]/img'))
        )
        small_img = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-smallimg"]/img'))
        )

        bg_name = './big.png'
        small_name = './small.png'
        # 获取图片地址
        bigimg = big_img.get_attribute("src")
        smallimg = small_img.get_attribute("src")
        # 下载背景大图保存到本地
        request.urlretrieve(bigimg, bg_name)
        # 下载滑块保存到本地
        request.urlretrieve(smallimg, small_name)

        # 二值化图片并进行比对
        target_rgb = cv2.imread(bg_name, 0)
        # 匹配缺口位置，并获取移动距离
        template_rgb = cv2.imread(small_name, 0)
        # res = cv2.matchTemplate(target_rgb, template_rgb, cv2.TM_CCOEFF_NORMED)
        res = cv2.matchTemplate(target_rgb, template_rgb, cv2.TM_SQDIFF_NORMED)
        value = cv2.minMaxLoc(res)
        value2 = value[2][0]
        value2 = value2 - 25

        ActionChains(self.browser).click_and_hold(small_img).perform()
        ActionChains(self.browser).move_by_offset(xoffset=value2, yoffset=0).perform()
        # 释放鼠标
        time.sleep(0.002)
        ActionChains(self.browser).move_by_offset(xoffset=-random.randint(1, 3), yoffset=0).perform()
        time.sleep(1)
        ActionChains(self.browser).release(on_element=small_img).perform()
        time.sleep(5)

    def get_cookies(self):
        cookies = self.browser.get_cookies()
        self.cookies = ''
        item_cookies = {}
        for cookie in cookies:
            if cookie.get('name') != 'tk_trace':
                item_cookies[cookie.get('name')] = cookie.get('value')
                self.cookies += '{}={};'.format(cookie.get('name'), cookie.get('value'))
        now = datetime.datetime.now()
        self.save_cookies()

    def save_cookies(self):
        cookies_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookies.json")
        f = open(cookies_path, 'w')
        do_at = int(time.time())
        item = {
            'do_at': do_at,
            'cookies': self.cookies
        }

        f.write(json.dumps(item))
        f.close()
        self.browser.close()

    # def __del__(self):
    #     self.browser.close()


if __name__ == '__main__':
    jd = JdLogin()
    jd.login()
    # print(jd._get_tracks(100))
    # jd.browser.close()




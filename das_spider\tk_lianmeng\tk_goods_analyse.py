import json
import time
import datetime
import requests
import urllib3
import models
from dateutil.parser import parse
from database import db, rds
from tk_login import Tk<PERSON>lientSpider
from utils.logx import logger

urllib3.disable_warnings()


class TkGoodsAnalyse(TkClientSpider):
    def __init__(self):
        super(TkGoodsAnalyse, self).__init__()

    def task(self, start_date, end_date):
        while end_date >= start_date:
            page_no = 1
            while True:
                if not self.get_goods_analyse(end_date, end_date, page_no):
                    break
                time.sleep(3)
                page_no += 1
            end_date = (parse(end_date) - datetime.timedelta(1)).date().__str__()

    def get_goods_analyse(self, start_date, end_date, page_no):
        url = "https://ad.alimama.com/openapi/param2/1/gateway.unionadv/mkt.rpt.lens.data.item_effect.json"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 Safari/537.36"
        }
        param = {
            "t": int(round(time.time() * 1000)),
            "_tb_token_": "5ebb3e3554175",
            "startDate": start_date,
            "endDate": end_date,
            "pageNo": page_no,
            "pageSize": 20,
            "period": "1d"
        }
        logger.info("get_goods_analyse request ", param)

        for i in range(3):
            req = requests.request(method="get", url=url, params=param, cookies=self.cookies, headers=headers, verify=False)
            if "<html>" not in req.text:
                break

            # 获取失败
            if i != 2:
                logger.warning(f"get_goods_analyse request invalid count {i}")
                self.account_login()
                time.sleep(2)
                continue
            else:
                logger.error(f"get_goods_analyse request error", req.text)

        # print(req.text)
        logger.info("get_goods_analyse response", req.text)
        j = req.json()
        # print(j)
        l = j.get("data").get("list")
        has_next = j.get("data").get("hasNext")
        for i in l:
            the_date = i.get("thedate")
            item_id = i.get("itemId")
            tk_goods_item = db.query(models.TKGoodsAnalyseReport).filter_by(the_date=the_date, item_id=item_id).first()
            if tk_goods_item:
                self.merge_goods_data(i, tk_goods_item)
            else:
                tk_goods_item = models.TKGoodsAnalyseReport()
                self.merge_goods_data(i, tk_goods_item)
                db.add(tk_goods_item)
            db.commit()
        return has_next and l

    @staticmethod
    def merge_goods_data(original_item, target_item):
        target_item.the_date = original_item.get("thedate")
        target_item.item_title = original_item.get("itemTitle")
        target_item.item_id = original_item.get("itemId")
        target_item.item_url = original_item.get("itemUrl")
        target_item.auction_url = original_item.get("auctionUrl")
        target_item.pre_commission_fee = original_item.get("preCommissionFee")
        target_item.pre_commission_rate = original_item.get("preCommissionRate")
        target_item.pre_service_fee = original_item.get("preServiceFee")
        target_item.pre_total_fee = original_item.get("preTotalFee")
        target_item.pre_service_rate = original_item.get("preServiceRate")
        target_item.cm_service_fee = original_item.get("cmServiceFee")
        target_item.cm_service_rate = original_item.get("cmServiceRate")
        target_item.cm_commission_fee = original_item.get("cmCommissionFee")
        target_item.cm_commission_rate = original_item.get("cmCommissionRate")
        target_item.cm_total_fee = original_item.get("cmTotalFee")
        target_item.alipay_amt = original_item.get("alipayAmt")
        target_item.alipay_num = original_item.get("alipayNum")
        target_item.coupon_dis_rate = original_item.get("couponDisRate")
        target_item.enter_shop_pv_tk = original_item.get("enterShopPvTk")
        target_item.clt_add_itm_cnt = original_item.get("cltAddItmCnt")
        target_item.cart_add_itm_cnt = original_item.get("cartAddItmCnt")

    def __del__(self):
        self.browser.close()

if __name__ == '__main__':
    TkGoodsAnalyse().task("2022-05-01", "2022-05-02")
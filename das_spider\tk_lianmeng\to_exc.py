import json
import os

import pandas as pd

map = {
    "thedate": "日期",
    "itemTitle": "商品名",
    "itemId": "商品ID",
    "itemUrl": "商品图片",
    "auctionUrl": "商品链接",
    "preCommissionFee": "付款佣金支出(元)",  # 付款佣金支出(元)
    "preCommissionRate": "付款佣金率",   # 付款佣金率
    "alipayAmt": "付款金额(元)",  # 付款金额(元)
    "alipayNum": "付款笔数",  # 付款笔数
    "couponDisRate": "商品折扣率",  # 商品折扣率
    "enterShopPvTk": "进店量",  # 进店量
    "cltAddItmCnt": "收藏宝贝量",  # 收藏宝贝量
    "cartAddItmCnt": "添加购物车量",   # 添加购物车量
  }

for root, dirs, files in os.walk("ad.alimama.com/openapi/param2/1/gateway.unionadv"):
    print(root, dirs, files)
    movie_list = []
    for file in files:
        # page = 8
        if file.startswith("."):
            continue
        file_path = os.path.join("ad.alimama.com/openapi/param2/1/gateway.unionadv", file)
        with open(file_path) as fw:
            res = json.load(fw)
        res = res.get('data').get("list")
        new_movies = pd.DataFrame(res, columns=map.keys(), dtype=str)
        new_movies.rename(columns=map, inplace=True)
        movie_list.append(new_movies)
    movies = pd.concat(movie_list)
    print(movies)

    movies.to_excel("aaa.xlsx", index=False)



# Define here the models for your spider middleware
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/spider-middleware.html
import base64
import time
import json
import os
import random
import sys
from fake_useragent import UserAgent
from scrapy import signals
from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait


# useful for handling different item types with a single interface
from itemadapter import is_item, ItemAdapter
from twisted.internet.error import TCPTimedOutError


class JdDetailSpiderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the spider middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_spider_input(self, response, spider):
        # Called for each response that goes through the spider
        # middleware and into the spider.

        # Should return None or raise an exception.
        return None

    def process_spider_output(self, response, result, spider):
        # Called with the results returned from the Spider, after
        # it has processed the response.

        # Must return an iterable of Request, or item objects.
        for i in result:
            yield i

    def process_spider_exception(self, response, exception, spider):
        # Called when a spider or process_spider_input() method
        # (from other spider middleware) raises an exception.

        # Should return either None or an iterable of Request or item objects.
        pass

    def process_start_requests(self, start_requests, spider):
        # Called with the start requests of the spider, and works
        # similarly to the process_spider_output() method, except
        # that it doesn’t have a response associated.

        # Must return only requests (not items).
        for r in start_requests:
            yield r

    def spider_opened(self, spider):
        spider.logger.info("Spider opened: %s" % spider.name)



# 设置user_agent池和cookies
class JdDetailDownloaderMiddleware(object):
    def __init__(self, crawler):
        super(JdDetailDownloaderMiddleware, self).__init__()
        self.ua = UserAgent()
        self.ua_type = crawler.settings.get("RANDOM_UA_TYPE", "random")
        self.browser = SeleniumMid()
    def read_cookies(self):
        cookies_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookies.json")
        if not os.path.exists(cookies_path):
            f = open(cookies_path, 'w')
            item = {
                'do_at': 1688010383,
                'cookies': []
            }
            f.write(json.dumps(item))
            f.close()
        with open(cookies_path, 'r') as f:
            cookies = f.read()
            json_cookies = json.loads(cookies).get('cookies','')
            if not json_cookies:
                self.browser.get_cookies()
                self.read_cookies()
            cookies = {}
            for cookie in json_cookies:
                cookies[cookie['name']] = cookie['value']
                # expires = cookie.get('expiry', 0)
                # timestamp = int(time.time())
                # if cookie.get('name') not in['_gia_d', 'shshshsID', 'mba_sid', '__jd_ref_cls', 'PCSYCityID', 'o2State', 'RT']:
                #     if cookie.get('name') == '__jdc' or timestamp - expires < 0 :
                #         cookies[cookie['name']] = cookie['value']
                    # else:
                    #     print("时间不匹配")
                    #     self.browser.get_cookies(url)
                    #     self.read_cookies(url)
                    #     break
        return cookies

    @classmethod
    def from_crawler(cls, crawler):
        return cls(crawler)

    def process_request(self, request, spider):
        def get_ua():
            return getattr(self.ua, self.ua_type)

        request.headers["User-Agent"] = get_ua()

        if request.meta.get("defeat") == "jingdong" :
            print("失败重试")
            self.browser.get_cookies()
        request.cookies = self.read_cookies()




PY3 = sys.version_info[0] >= 3


def base64ify(bytes_or_str):
    if PY3 and isinstance(bytes_or_str, str):
        input_bytes = bytes_or_str.encode('utf8')
    else:
        input_bytes = bytes_or_str

    output_bytes = base64.urlsafe_b64encode(input_bytes)
    if PY3:
        return output_bytes.decode('ascii')
    else:
        return output_bytes

# 代理IP中间件
class ProxyMiddleware(object):
    def process_request(self, request, spider):
        # 代理服务器(产品官网 www.16yun.cn)
        proxyhost = "u9672.5.tn.16yun.cn"
        proxyport = "6441"

        # 代理验证信息
        proxyuser = "16GJRXIC"
        proxypass = "118837"

        request.meta['proxy'] = "http://{0}:{1}".format(proxyhost, proxyport)

        # 添加验证头
        encoded_user_pass = base64ify(proxyuser + ":" + proxypass)
        request.headers['Proxy-Authorization'] = 'Basic ' + str(encoded_user_pass)

        # 设置IP切换头(根据需求)
        tunnel = random.randint(1, 10000)
        request.headers['Proxy-Tunnel'] = str(tunnel)

        # 每次访问后关闭TCP链接，强制每次访问切换IP
        request.headers['Connection'] = "Close"

        request.headers['User-Agent'] = UserAgent().random
        a = request.url
        if not ('https://item.jd.com' in request.url):
            tunnel = random.randint(1, 10000)
            request.headers['Proxy-Tunnel'] = str(tunnel)

    def process_response(self, request, response, spider):
        if not ('https://item.jd.com' in response.url):
            print()
            self.process_request(request, spider)

            # tunnel = random.randint(1, 10000)
            # request.headers['Proxy-Tunnel'] = str(tunnel)
            # # 每次访问后关闭TCP链接，强制每次访问切换IP
            # request.headers['Connection'] = "Close"
        return response




#selenium获取cookie
class SeleniumMid():
    def __init__(self):
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        chrome_options.add_argument('--no-sandbox')
        # chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-dev-shm-usage')
        self.browser = webdriver.Chrome(options=chrome_options)
        # self.browser = webdriver.Chrome()
        self.wait = WebDriverWait(self.browser, 20)
        self.url = 'https://item.jd.com/10024525977797.html'
        self.cookies = ""

    # def process_request(self, request, spider):
    def get_cookies(self):
        self.browser.delete_all_cookies()
        self.browser.get(self.url)  # 请求网址
        if self.browser.current_url != self.url:
            self.browser.get(self.url)  # 请求网址
        refresh_cookie = self.browser.get_cookies()
        # old_cookie=self.read_cookies()
        # old_cookie.extend(refresh_cookie)#合并cookie
        # cookie_dic = dict()
        # for i in refresh_cookie:
        #     cookie_dic[i.get('name')] = i
        self.cookies = refresh_cookie
        # for v in cookie_dic.values():
        #     self.cookies.append(v)
        self.save_cookies()

    def read_cookies(self):
        cookies_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookies.json")
        if not os.path.exists(cookies_path):
            f = open(cookies_path, 'w')
            item = {
                'do_at': 1688010383,
                'cookies': []
            }
            f.write(json.dumps(item))
            f.close()
            return list()
        f = open(cookies_path, 'r')
        cookies = f.read()
        if not cookies:
            return list()

        old_cookie = json.loads(cookies).get('cookies')
        f.close()
        return old_cookie


    def save_cookies(self):
        cookies_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookies.json")
        f = open(cookies_path, 'w')
        do_at = int(time.time())
        item = {
            'do_at': do_at,
            'cookies': self.cookies
        }
        f.write(json.dumps(item))
        f.close()
        print("cookie保存成功")
        # self.browser.close()





import time
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, UniqueConstraint, Index, event

_Base = declarative_base()


class BaseModel(_Base):
    __abstract__ = True

    id = Column(Integer(), primary_key=True)
    created_at = Column(DateTime(), comment="创建时间")
    updated_at = Column(DateTime(), comment="更新时间")
    deleted_at = Column(DateTime(), comment="删除时间")
    remark = Column(String(55), comment="备注")


class DyComment(BaseModel):
    __tablename__ = "das_dy_comment"
    # 自己添加字段

    # 原表字段
    # 用户相关
    app_id = Column(Integer(), comment="app_id")
    user_name = Column(String(55), comment="用户昵称")
    user_avatar = Column(String(255), comment="用户头像")
    anonymous = Column(Integer(), comment="匿名")
    # 评论相关
    comment_id = Column(String(32), index=True, nullable=False, comment="评论id")
    parent_id = Column(String(32), comment="父评论id")
    content = Column(String(255), comment="内容")
    photos_url = Column(Text(), comment="评论图片(多张图片使用','分割)")
    videos_main_url = Column(Text(), comment="评论视频(多份视频使用','分割)")
    orig_content = Column(String(255), comment="")
    shop_reply = Column(String(255), comment="商家回复内容")
    comment_time = Column(DateTime(), comment="评论时间")
    create_time = Column(DateTime(), comment="评论创建时间")
    is_append = Column(Integer(), comment="")     # 是否是追评
    reply_time = Column(DateTime(), comment="商家回复时间")
    appends_comment_time = Column(DateTime(), comment="追评时间")
    appends_photos_url = Column(Text(), comment="追评图片(多张图片使用','分割)")
    appends_videos_main_url = Column(Text(), comment="追评视频(多份视频使用','分割)")
    appends_content = Column(String(255), comment="追评内容")
    # 订单相关
    order_id = Column(String(32), index=True, nullable=False, comment="订单号")
    is_abnormal_order = Column(Integer(), comment="是否是异常订单")
    # 商品相关
    product_id = Column(String(32), index=True, nullable=False, comment="商品id")
    product_name = Column(String(255), comment="商品名称")
    product_img = Column(String(255), comment="商品图片链接")
    sku = Column(String(255), comment="sku名")
    # 其他
    likes = Column(Integer(), comment="")
    rank = Column(Integer(), comment="")
    rank_shop = Column(Integer(), comment="")
    rank_logistic = Column(Integer(), comment="")
    status = Column(Integer(), comment="")
    appeal_display_status = Column(Integer(), comment="申诉状态")
    tags_rank_info_name = Column(String(32), comment="评价值中文释义")
    tags_rank_info_rank_tag = Column(Integer(), comment="评价值 1差评2中评3好评")
    tags_negative_tags_name = Column(String(55), comment="评论标签名")
    tags_negative_tags_desc = Column(String(55), comment="评论标签描述")

    # photos = Column(Integer(), comment="")
    # appends = Column(Integer(), comment="")
    # videos = Column(Integer(), comment="")
    # tags = Column(, comment="")
    # product = Column(, comment="")

    def __repr__(self):
        return self.comment_id


@event.listens_for(DyComment, 'before_insert')
def receive_before_create(mapper, connection, target):
    "listen for the 'before_insert' event"
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(DyComment, 'before_update')
def receive_before_update(mapper, connection, target):
    "listen for the 'before_update' event"
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')

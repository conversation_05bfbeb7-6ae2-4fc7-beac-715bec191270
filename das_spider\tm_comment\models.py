import time
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, UniqueConstraint, Index, event
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, LONGTEXT, MEDIUMTEXT, SMALLINT, TEXT, TINYINT, VARCHAR


_Base = declarative_base()


class BaseModel(_Base):
    __abstract__ = True

    id = Column(Integer(), primary_key=True)  # id 主键，默认自增，可以通过autoincrement参数设置
    created_at = Column(DateTime(), comment="创建时间")
    updated_at = Column(DateTime(), comment="更新时间")
    deleted_at = Column(DateTime(), comment="删除时间")
    remark = Column(String(55), comment="备注")


class TianmaoComment(BaseModel):
    __tablename__ = 'tianmao_comment'
    __table_args__ = {'comment': '天猫评论\\n'}

    username = Column(VARCHAR(30), comment='昵称')
    userid = Column(BIGINT(20), comment='用户id')
    do_time = Column(DateTime, comment='爬虫执行时间')
    user_level = Column(VARCHAR(255), comment='用户等级')
    order_id = Column(BIGINT(20), comment='订单号')
    good_name = Column(VARCHAR(255), comment='商品名称')
    good_id = Column(BIGINT(20), comment='商品id')
    sku_info = Column(String(30), comment='sku名称')
    sku_id = Column(VARCHAR(60), comment='skuId')
    first_comment = Column(VARCHAR(255), comment='首次评论')
    tags = Column(String(255), comment='情感标签')
    sentiment = Column(INTEGER(1), comment='表示情感极性分类结果, 0:负向，1:中性，2:正向')
    first_comment_label_one = Column(VARCHAR(20), comment='首次评论标签1')
    first_comment_label_two = Column(VARCHAR(20), comment='首次评论标签2')
    first_comment_id = Column(VARCHAR(55), index=True, comment='首次评论id')
    first_comment_time = Column(DateTime, comment='首次评论时间')
    first_comment_pngs = Column(VARCHAR(255), comment='首次评论图片链接')
    first_comment_video = Column(VARCHAR(255), comment='首次评论视频链接')
    first_callback = Column(VARCHAR(500), comment='店家首次回复')
    first_callback_time = Column(DateTime, comment='店家首次回复时间')
    append_comment = Column(VARCHAR(255), comment='追加评论')
    append_comment_time = Column(DateTime, comment='追加评论时间')
    append_comment_pngs = Column(VARCHAR(255), comment='追加评论图片')
    append_comment_id = Column(VARCHAR(55), comment='追加评轮id')
    append_comment_label_one = Column(VARCHAR(20), comment='追加评论标签1')
    append_comment_label_two = Column(VARCHAR(20), comment='追加评论标签2')
    append_callback = Column(VARCHAR(500), comment='追加评论回复')
    append_callback_time = Column(DateTime, comment='追加评论回复时间')
    append_comment_video = Column(VARCHAR(255), comment='追加评论视频')
    first_callback_name = Column(VARCHAR(55), comment='店家首次回复人昵称')
    append_callback_name = Column(VARCHAR(55), comment='追加回复人昵称')

    def __repr__(self):
        return self.first_comment_id


class JkMessageRecordDetail(BaseModel):
    __tablename__ = 'jk_message_record_detail'
    __table_args__ = {'comment': '集客短信记录'}

    message_id = Column(VARCHAR(30), comment='短信记录id', unique=True)
    content = Column(VARCHAR(255), comment='内容')
    ouid = Column(VARCHAR(55), comment='用户id')
    send_time = Column(DateTime, comment='发送时间')
    service_name = Column(VARCHAR(255), comment='服务名称')
    task_id = Column(INTEGER(11), comment='任务id')
    task_name = Column(VARCHAR(55), comment='任务名')
    task_type = Column(INTEGER(11), comment='任务类型')
    task_type_str = Column(VARCHAR(55), comment='任务类型-字符串')
    tid = Column(VARCHAR(55), comment='订单号')
    word_num = Column(INTEGER(11), comment='短信内容字数')
    word_page = Column(INTEGER(11), comment='短信计费条数')
    wx_nick = Column(VARCHAR(55))
    phone_num = Column(VARCHAR(55), comment=' 发送手机号')
    send_state = Column(VARCHAR(55), comment='发送状态')
    ttime = Column(DateTime, comment='下单时间')
    receive_tel = Column(VARCHAR(55), comment='收货人手机号')


@event.listens_for(TianmaoComment, 'before_insert')
def receive_before_create(mapper, connection, target):
    "listen for the 'before_insert' event"
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(JkMessageRecordDetail, 'before_update')
def receive_before_update(mapper, connection, target):
    "listen for the 'before_update' event"
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(JkMessageRecordDetail, 'before_insert')
def receive_before_create(mapper, connection, target):
    "listen for the 'before_insert' event"
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(TianmaoComment, 'before_update')
def receive_before_update(mapper, connection, target):
    "listen for the 'before_update' event"
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')

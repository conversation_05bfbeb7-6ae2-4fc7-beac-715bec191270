import json
import time
import re
import traceback
from jd_detail.utils.baidu_ocr import BaiduOCR
import scrapy
from jd_detail.items import JdDetailItem
from threading import Lock
from jd_detail.pipelines import JdMySQLPipeline


class HomepageSpider(scrapy.Spider):
    name = "jd"
    allowed_domains = ["item.jd.com"]

    def __init__(self, collection_time=None, *args, **kwargs):
        self.db = JdMySQLPipeline()
        self.collection_time = collection_time
        super().__init__(*args, **kwargs)

    def start_requests(self):
        if not self.collection_time:
            self.collection_time = time.strftime("%Y-%m-%d", time.localtime())
        items = []
        con = 0
        while True:
            db_item = self.db.get_goods_url(self.collection_time, con)
            if db_item:
                items.extend(db_item)
                con += 50
            else:
                break
        print("爬取链接数:", len(items))
        for item in items:
            id = item.get('id')
            url = item.get('goods_url')
            time.sleep(1)
            yield scrapy.Request(url=url, callback=self.parse, dont_filter=True, meta={'url': url, 'id': id})

    def parse(self, response, *args, **kwargs):
        sku_name = "".join(response.xpath("//div[@class='sku-name']/text()").extract()).strip()  # 取sku名字
        img_src = response.xpath("//img[@id='spec-img']/@data-origin").extract_first()
        if not img_src:
            id = response.meta.get("id")
            url = response.meta.get("url")
            goods_id = url.split('/')[-1].split('.')[0]
            defeat = response.meta.get("defeat")
            if url and not defeat:
                yield scrapy.Request(url=url, callback=self.parse, dont_filter=True,
                                     meta={'defeat': "jingdong", 'url': url, 'id': id})
            else:
                self.db.insert_jd_detail(goods_id, url, sku_name, img_src, choose_item=None, specification=None,
                                         packing_list=None, product_status=1)
                self.db.update_collection_time(id)
                print("失败链接存入数据库中,并更新最后爬取时间", url)
        else:
            img_src = 'https:' + img_src.strip().replace('.avif', '')
            choose_list = response.xpath("//div[contains(@class,'selected')]/@data-value").extract()  # 取选择的项
            specification_list = []
            specification_elements = response.xpath('//dl[@class="clearfix"]')
            for specification_ele in specification_elements:
                specification_dt = specification_ele.xpath('./dt/text()').extract_first().strip()
                specification_dd = specification_ele.xpath('./dd/text()').extract_first().strip()
                specification_list.append(specification_dt+specification_dd)
            packing_list = response.xpath("//h3[text()='包装清单']/following-sibling::p/text()").extract_first()
            # specification = json.dumps(specification_list)
            print(sku_name, img_src, choose_list,specification_list,packing_list)
            is_delist = response.xpath("//div[starts-with(text(),'该商品已下柜')]").extract()  # 判断是否下架
            if is_delist:
                print("该商品已下柜")
                product_status = 2  # 下架商品值为2
            else:
                product_status = 0
            jditem = JdDetailItem()
            jditem['id'] = response.meta.get("id")  # 获取表的id
            jditem['goods_id'] = response.url.split('/')[-1].split('.')[0]
            jditem['goods_url'] = response.url
            jditem['sku_name'] = sku_name
            jditem['choose_list'] = choose_list
            jditem['img_src'] = img_src
            jditem['specification'] = str(specification_list)
            jditem['packing_list'] = packing_list
            jditem['product_status'] = product_status
            yield jditem

    def set_goods_identify_info(self):
        a = 1
        ocr = BaiduOCR()
        goods_images = list(self.db.get_goods_image())
        for item in goods_images:
            try:
                print("正在识别第{}张图片".format(a))
                a += 1
                jd_id, image_src = item
                result = ocr.ocr_image(image_src)
                if result and "error_code" not in result:
                    self.db.update_jd_detail(jd_id, result)
                else:
                    result = json.loads(result)
                    print(f"识别失败，goods_id为：{jd_id}，错误原因：{result.get('error_msg')}")
            except Exception as e:
                print("set_goods_identify_info报错啦!", e)
                continue

    def parse_goods_price(self):
        base_price_re_rule = '(?P<price>\d+(\.\d+)?)'
        price_re_rules = [
            {
                "rule": f"[到手于价]{{2,}}(：|￥)*{base_price_re_rule}",
                # "rule": f"到手(价)?(：|￥)?{base_price_re_rule}",
                "mode": "precise_matching",
                "weight": 99,
                "type": 1
            },
            {
                "rule": f"券后(价)?(：|￥)?{base_price_re_rule}",
                "mode": "precise_matching",
                "weight": 99,
                "type": 1
            },
            {
                "rule": f"均价?(：|￥)?{base_price_re_rule}",
                "mode": "precise_matching",
                "weight": 99,
                "type": 1
            },
            {
                "rule": f"[到手于价]{{2,}}(：|￥)?.*?{base_price_re_rule}",
                "mode": "fuzzy_matching",
                "weight": 10,
                "type": 1
            },
            {
                "rule": f"均价(：|￥)?.*?{base_price_re_rule}",
                "mode": "fuzzy_matching",
                "weight": 10,
                "type": 1
            },
            {
                "rule": f"单件.*?(：|￥)?.*?{base_price_re_rule}",
                "mode": "fuzzy_matching",
                "weight": 10,
                "type": 1
            },
            {
                "rule": f"{base_price_re_rule}任选(?P<number>\d+)",
                "mode": "fuzzy_matching",
                "weight": 99,
                "type": 2
            },

        ]
        goods_text_list = self.db.get_goods_text()
        for goods_text_item in goods_text_list:
            text = ""
            price_dict = {}
            if not goods_text_item[1]:
                continue
            words_result = json.loads(goods_text_item[1]).get("words_result")
            if not words_result:
                print(goods_text_item[0])
                input("继续？")
                continue
            for word_result in words_result:
                # print(i)
                text += word_result.get("words")
            for rule_dic in sorted(price_re_rules, key=lambda x: x.get("weight"), reverse=True):
                re_result = re.search(rule_dic.get("rule"), text)
                if re_result:
                    if rule_dic.get("type") == 2:
                        price_dict.update({
                            "price": float(re_result.groupdict().get("price")) / float(re_result.groupdict().get("number")),
                            "mode": rule_dic.get("mode")
                        })
                    else:
                        price_dict.update({
                            "price": float(re_result.groupdict().get("price")),
                            "mode": rule_dic.get("mode")
                        })
                    break
            print(goods_text_item[0], text, "价格为猜测为: ", price_dict)
            self.db.update_goods_price(goods_text_item[0], price_dict.get("price"), price_dict.get("mode"))
            if not price_dict:
                input("继续？")


if __name__ == "__main__":
    image_url = "https://img13.360buyimg.com/n1/s450x450_jfs/t1/125056/21/39322/101993/64987fc7F4978d5c8/730f1a16ebb90763.jpg"

    ocr = HomepageSpider()
    # result = ocr.ocr_image(image_url)
    # ocr.set_goods_identify_info()
    ocr.parse_goods_price()

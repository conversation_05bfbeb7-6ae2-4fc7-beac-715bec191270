# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class JdDetailItem(scrapy.Item):
    # define the fields for your item here like:
    id = scrapy.Field()
    goods_id = scrapy.Field()  # 商品id
    goods_url = scrapy.Field()  # 商品链接
    sku_name = scrapy.Field()  # sku名称
    choose_list = scrapy.Field()  # 商品选择项
    img_src = scrapy.Field()  # 商品图片链接
    specification = scrapy.Field()  # 商品规格
    packing_list = scrapy.Field()  # 包装清单
    product_status = scrapy.Field()  # 状态|0正常|1链接存在问题|2链接能访问,商品已下架

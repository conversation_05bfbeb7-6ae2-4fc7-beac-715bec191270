import os
import time
import json
import json
import requests
import shutil
import pandas as pd
from lxml import etree
from retrying import retry
from models import KocInfo
from database import db
import base64
import json
import os
import random
import re
import time
import datetime

import cv2
import requests
import urllib3
import numpy as np
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions \
    as Ec
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import ActionChains


cookies = {
    # chrome
    'xhsTrackerId=7e487107-b20c-4b15-cb8e-a1fa9756a662; smidV2=20220322174705ed8839da5eb363cf77457e5f373ffdec001d065227fb18560; xhsTracker=url=noteDetail&searchengine=baidu; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_exp1,ques_clt2; timestamp2=20220406d4c0ac00f7609cbfafdcc1ab; timestamp2.sig=j3Fs0cGgKsL2_eNCh_V-5U5NIXrcIaPBwJRaJSkzLVs': "",
    # 无痕chrome
    'xhsTrackerId=c17e9666-925d-437e-c3d9-3be3d105f3fd; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_exp1,ques_exp1; timestamp2=20220406d4c0ac00f7609cbfafdcc1ab; timestamp2.sig=j3Fs0cGgKsL2_eNCh_V-5U5NIXrcIaPBwJRaJSkzLVs': "",
    # safari
    'timestamp2=202204069286b11ef33ef5f7b971464c; timestamp2.sig=MvaeJtNTHDliQSwTsBcHpEnHoN95k5f-KnFsXQPjJdQ; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_exp1,ques_exp2; xhsTrackerId=30c72702-027a-475c-c769-25b41470ebf4': "",
    # 祖旭
    'xhsTrackerId=a5b1dd29-2d95-4980-c922-1e4c889cd124; xhsTracker=url=noteDetail&searchengine=baidu; timestamp2=20220331223f71de26a26f3e8997b0d6; timestamp2.sig=oZcsrA_Fvy9v2dNcQz7ZvXisVofZQJYMQzHM2u7MHJI; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_clt1,ques_clt1': "",
    'xhsTrackerId=d57070be-2407-44c3-c490-430e1aa66e05; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_clt1,ques_exp1; timestamp2=20220406223f71de26a26f3efc03f1c9; timestamp2.sig=n0ToYfP6g6WprULehIfxdKqtanxnDyiC_p8LMtoeKhM': "",
    'xhsTrackerId=418e53d3-2c69-4085-c0fe-79aa0989fef4; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_clt1,ques_exp1; timestamp2=20220406ed56d1d6e5f6c95d00db823a; timestamp2.sig=oLV4uxdPrLwmwZUoO3GtXKV1NFCeXd0IPMDjprKhNXk': "",
    'xhsTrackerId=a3f92753-14fc-43f0-c3ce-a045006e3392; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_exp1,ques_clt1; timestamp2=20220406ed56d1d6e5f6c95d00db823a; timestamp2.sig=oLV4uxdPrLwmwZUoO3GtXKV1NFCeXd0IPMDjprKhNXk': "",
    # 小辉
    'xhsTrackerId=80c16185-a1ad-435e-c38c-868aca624f87; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_clt1,ques_clt1; timestamp2=20220406964488296c42b8adf4c1d24a; timestamp2.sig=YAA6aYsyureREV1pmyOqTma9pEzL1hQF8appiiAPti4': "",
    'xhsTrackerId=41189b90-8ada-4e42-ce78-d2ad7292d8eb; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_clt1,ques_clt2; timestamp2=20220406964488296c42b8adf4c1d24a; timestamp2.sig=YAA6aYsyureREV1pmyOqTma9pEzL1hQF8appiiAPti4': "",
    'xhsTrackerId=42e085ba-e664-4887-c89a-c425514112b2; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_clt1,ques_exp1; timestamp2=2022040645ab8538dc45e5c39276d09e; timestamp2.sig=GvTyMH6KtM7XWvJAs1woyBHD8DCFkH2YM0WNM2KFfcs': "",
    'xhsTrackerId=f5207d43-3169-427f-c23e-6c1c6e3bd6e5; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_exp1,ques_exp2; timestamp2=2022040645ab8538dc45e5c39276d09e; timestamp2.sig=GvTyMH6KtM7XWvJAs1woyBHD8DCFkH2YM0WNM2KFfcs': "",
    # 彭琰
    'xhsTrackerId=2fd3f53f-1316-4ad5-cad7-dc6cd5fdb244; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_clt1,ques_exp2; timestamp2=20220406d39a4d2cd5d4e12400dffbbd; timestamp2.sig=zxt0gESB6Wa6RmGQ_Si0nDIClgYanXhTv_KSey7Ylk0': "",
    'xhsTrackerId=07c6dafb-8e1e-472d-c7c7-f658f08aeaf6; xhsTracker=url=noteDetail&searchengine=baidu; smidV2=202203251443286406022613104d6d68b7fbec4ac4a74f001d85fe83da56be0; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_clt1,ques_clt2; timestamp2=20220406663241b935a07b12b924c543; timestamp2.sig=HMeQOOopNbkNDFA-4uTOdEarHQ6-4zXQW3RwBKi0lI4': "",
    'xhsTrackerId=7f18daab-9831-4583-c52d-bfb64c4f406f; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_exp1,ques_exp1; timestamp2=20220406663241b935a07b12b924c543; timestamp2.sig=HMeQOOopNbkNDFA-4uTOdEarHQ6-4zXQW3RwBKi0lI4': "",
    'xhsTrackerId=616216cd-5e81-4933-c92a-4d7ff55e0ff4; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_exp1,ques_clt1; timestamp2=20220406d39a4d2cd5d4e12400dffbbd; timestamp2.sig=zxt0gESB6Wa6RmGQ_Si0nDIClgYanXhTv_KSey7Ylk0': "",
    # 炫锴
    'xhsTrackerId=e645e5c8-d43c-4271-c2fe-4ae84b76e053; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_clt1,gif_exp1,ques_exp2; timestamp2=20220406290d54316e2add8fcf4a0b05; timestamp2.sig=W8_4EHYCugp7CSn2z9wlrhkGy7hO0k0W2H5K8r_eDfE': "",
    'xhsTrackerId=4fee2319-9df1-4469-c402-0e6bcf348a8f; extra_exp_ids=supervision_exp,supervision_v2_exp,commentshow_exp1,gif_clt1,ques_clt2; timestamp2=20220406290d54316e2add8fcf4a0b05; timestamp2.sig=W8_4EHYCugp7CSn2z9wlrhkGy7hO0k0W2H5K8r_eDfE': "",
}


def get_cookie():
    for k, v in cookies.items():
        if not v or v <= datetime.datetime.now().__str__():
            return k
    print("cookie池耗尽")
    time.sleep(60)
    return get_cookie()


cookie = get_cookie()


def get_userid():
    count = 0
    while True:
        count += 1
        res = db.query(KocInfo.xhs_userid).filter(KocInfo.xhs_userid!=None, KocInfo.xhs_redid==None).limit(10).all()
        print(count)
        if len(res) == 0:
            break
        for userid in res:
            get_redid_by_html(userid[0])
            # time.sleep(1)
        db.commit()


def retry_if_io_error(exception):
    return isinstance(exception, json.decoder.JSONDecodeError) or isinstance(exception, ConnectionError)


@retry(wait_fixed=5000, retry_on_exception=retry_if_io_error)
def get_redid_by_html(userid):
    '''
     根据userid，从小红书用户详情页获取redid、note_count数据
    '''
    global cookie
    url = f"https://www.xiaohongshu.com/user/profile/{userid}"
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36",
        "Cookie": cookie,
        "referer": "https://www.xiaohongshu.com/web-login/canvas?redirectPath=http%3A%2F%2Fwww.xiaohongshu.com%2Fuser%2Fprofile%2F5b168b0311be106c73115839",
    }
    req = requests.request(method='get', url=url, headers=headers)
    if req.status_code == 404:
        print("请求异常, code: %s"%req.status_code)
        db.query(KocInfo).filter_by(xhs_userid=userid).update({"xhs_redid": 0,
                                                               "xhs_note_count": 0})
        return
    elif req.status_code != 200:
        print(userid)
        # print(headers['Cookie'])
        cookies[cookie] = (datetime.datetime.now() + datetime.timedelta(minutes=5)).__str__()
        cookie = get_cookie()
        print("请求异常, code: %s"%req.status_code)
        raise ConnectionError("请求异常, code: %s"%req.status_code)
    try:
        selectors = etree.HTML(req.text)
        text = selectors.xpath("//script[last()]")[-1].text
        dispose_text = text.split("=", 1)[-1].replace("undefined", '""')
        j = json.loads(dispose_text)
        id = j.get("Main").get("userDetail").get("id")
        if id != userid:
            print(userid)
            raise Exception("程序bug")
        redid = j.get("Main").get("userDetail").get("redId")
        note_count = j.get("Main").get("userDetail").get("notes")
        db.query(KocInfo).filter_by(xhs_userid=userid).update({"xhs_redid": redid,
                                                                "xhs_note_count": note_count})
    except Exception as ec:
        print(userid)
        # print(headers['Cookie'])
        cookies[cookie] = (datetime.datetime.now() + datetime.timedelta(minutes=2)).__str__()
        cookie = get_cookie()
        # print(req.text)
        # url = 'https://www.xiaohongshu.com/user/profile/' + userid
        # XhsSpider().open_url(url)
        raise ec
        # print(userid)
        # print(text)


def save_to_mysql(filepath):
    '''
      把从小红书通讯录中获取到的数据入库
      filepath: 文件夹路径（可以传入多层级文件夹）
    '''
    phone_dict = dict()
    # phone_list = list()
    count = 0
    for root, dirs, files in os.walk(filepath):
        for file in files:
            if file.startswith("."):
                continue
            file = os.path.join(root, file)
            with open(file, 'r', encoding='utf8') as fr:
                # file_content = fr.read()
                try:
                    file_content = json.load(fr)
                except:
                    print("error file", file)
                    raise
                # print(type(file_content))
                for i in file_content.get("data"):
                    phone_dict[i.get('phone').lstrip('86')] = i
                    # phone_list.append(i.get('phone').lstrip('86'))
                    koc_info = db.query(KocInfo).filter_by(receiver_mobile=i.get('phone').lstrip('86')).first()
                    if koc_info:
                        count += 1
                        # print(i.get('phone').lstrip('86'))
                        koc_info.xhs_is_register = True
                        koc_info.xhs_nickname = i.get("nickname")
                        koc_info.xhs_userid = i.get("userid")
                    else:
                        print(i.get('phone').lstrip('86'))
                        raise Exception("有bug")
                db.commit()
            print(file)
            print(f"已修改{count}条数据")
    # print(phone_dict.values())
    # df = pd.DataFrame(phone_dict.values())
    # df = pd.DataFrame(phones, columns=['phone'])
    # df.to_csv("111.csv")
    # stmt = insert(KocInfo).values(item).on_duplicate_key_update(i2)
    # self.mysql.execute(stmt)
    # print(len(phone_list))
    # print(len(set(phone_list)))
    # print(phone_list)


if __name__ == '__main__':
    save_to_mysql('userfile/14')
    #
    # get_redid_by_html("5a24e56de8ac2b03af81537f")
    # get_userid()
    # db.close()
    # userid = '5a24e56de8ac2b03af81537f'
    # url = 'https://www.xiaohongshu.com/user/profile/' + userid
    # XhsSpider().open_url(url)

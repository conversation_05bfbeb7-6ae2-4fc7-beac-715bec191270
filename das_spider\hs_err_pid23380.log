#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 257949696 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=23380, tid=18748
#
# JRE version:  (17.0.11+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://git.aizinger.com': 

Host: AMD Ryzen 7 4800H with Radeon Graphics         , 16 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
Time: Mon Jul  7 17:48:10 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5678) elapsed time: 0.015749 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001ba186287a0):  JavaThread "Unknown thread" [_thread_in_vm, id=18748, stack(0x000000714ca00000,0x000000714cb00000)]

Stack: [0x000000714ca00000,0x000000714cb00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6877f9]
V  [jvm.dll+0x8411aa]
V  [jvm.dll+0x842e2e]
V  [jvm.dll+0x843493]
V  [jvm.dll+0x249fdf]
V  [jvm.dll+0x6845c9]
V  [jvm.dll+0x678e7a]
V  [jvm.dll+0x30ab4b]
V  [jvm.dll+0x311ff6]
V  [jvm.dll+0x361a5e]
V  [jvm.dll+0x361c8f]
V  [jvm.dll+0x2e0978]
V  [jvm.dll+0x2e18e4]
V  [jvm.dll+0x811c71]
V  [jvm.dll+0x36f7c8]
V  [jvm.dll+0x7f05f6]
V  [jvm.dll+0x3f398f]
V  [jvm.dll+0x3f5541]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007fff80e0efd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001ba186975e0 GCTaskThread "GC Thread#0" [stack: 0x000000714cb00000,0x000000714cc00000] [id=21888]
  0x000001ba186a9a40 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000714cc00000,0x000000714cd00000] [id=20616]
  0x000001ba186aa360 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000714cd00000,0x000000714ce00000] [id=24096]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff805c1547]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001ba186243c0] Heap_lock - owner thread: 0x000001ba186287a0

Heap address: 0x000000070a000000, size: 3936 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000070a000000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x000001ba2c8f0000,0x000001ba2d0a0000] _byte_map_base: 0x000001ba290a0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001ba18699b10, (CMBitMap*) 0x000001ba18699b50
 Prev Bits: [0x000001ba2d850000, 0x000001ba315d0000)
 Next Bits: [0x000001ba315d0000, 0x000001ba35350000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.010 Loaded shared library D:\software\PyCharm 2024.1.6\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7315c0000 - 0x00007ff7315ca000 	D:\software\PyCharm 2024.1.6\jbr\bin\java.exe
0x00007fffe0c10000 - 0x00007fffe0e08000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fffe08c0000 - 0x00007fffe0982000 	C:\Windows\System32\KERNEL32.DLL
0x00007fffde5b0000 - 0x00007fffde8a6000 	C:\Windows\System32\KERNELBASE.dll
0x00007fffde230000 - 0x00007fffde330000 	C:\Windows\System32\ucrtbase.dll
0x00007fffbde40000 - 0x00007fffbde57000 	D:\software\PyCharm 2024.1.6\jbr\bin\jli.dll
0x00007fffcb330000 - 0x00007fffcb34b000 	D:\software\PyCharm 2024.1.6\jbr\bin\VCRUNTIME140.dll
0x00007fffe0360000 - 0x00007fffe04fd000 	C:\Windows\System32\USER32.dll
0x00007fffde0d0000 - 0x00007fffde0f2000 	C:\Windows\System32\win32u.dll
0x00007fffdf540000 - 0x00007fffdf56b000 	C:\Windows\System32\GDI32.dll
0x00007fffde330000 - 0x00007fffde44a000 	C:\Windows\System32\gdi32full.dll
0x00007fffde460000 - 0x00007fffde4fd000 	C:\Windows\System32\msvcp_win.dll
0x00007fffcd100000 - 0x00007fffcd39a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007fffe05e0000 - 0x00007fffe067e000 	C:\Windows\System32\msvcrt.dll
0x00007fffe0990000 - 0x00007fffe09bf000 	C:\Windows\System32\IMM32.DLL
0x0000000054330000 - 0x000000005433d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fffded00000 - 0x00007fffdedaf000 	C:\Windows\System32\ADVAPI32.dll
0x00007fffdea70000 - 0x00007fffdeb0f000 	C:\Windows\System32\sechost.dll
0x00007fffdebd0000 - 0x00007fffdecf3000 	C:\Windows\System32\RPCRT4.dll
0x00007fffde8e0000 - 0x00007fffde907000 	C:\Windows\System32\bcrypt.dll
0x00007fffcd6a0000 - 0x00007fffcd7a5000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fffdedd0000 - 0x00007fffdf53f000 	C:\Windows\System32\SHELL32.dll
0x00007fffe09c0000 - 0x00007fffe0a15000 	C:\Windows\System32\SHLWAPI.dll
0x00007fffdda20000 - 0x00007fffdda2a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fffcc8d0000 - 0x00007fffcc8dc000 	D:\software\PyCharm 2024.1.6\jbr\bin\vcruntime140_1.dll
0x00007fff11cb0000 - 0x00007fff11d3d000 	D:\software\PyCharm 2024.1.6\jbr\bin\msvcp140.dll
0x00007fff802d0000 - 0x00007fff80f53000 	D:\software\PyCharm 2024.1.6\jbr\bin\server\jvm.dll
0x00007fffde1e0000 - 0x00007fffde22b000 	C:\Windows\System32\POWRPROF.dll
0x00007fffd7f20000 - 0x00007fffd7f29000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007fffe0780000 - 0x00007fffe07eb000 	C:\Windows\System32\WS2_32.dll
0x00007fffd3610000 - 0x00007fffd3637000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffdddc0000 - 0x00007fffdddd2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fffde030000 - 0x00007fffde042000 	C:\Windows\System32\kernel.appcore.dll
0x00007fffcc750000 - 0x00007fffcc75a000 	D:\software\PyCharm 2024.1.6\jbr\bin\jimage.dll
0x00007fffdc360000 - 0x00007fffdc544000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fffc9fe0000 - 0x00007fffca014000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fffde100000 - 0x00007fffde182000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fffb7890000 - 0x00007fffb78b5000 	D:\software\PyCharm 2024.1.6\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\PyCharm 2024.1.6\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Program Files (x86)\360\360Safe\safemon;D:\software\PyCharm 2024.1.6\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://git.aizinger.com': 
java_class_path (initial): D:/software/PyCharm 2024.1.6/plugins/vcs-git/lib/git4idea-rt.jar;D:/software/PyCharm 2024.1.6/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4127195136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4127195136                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_172
PATH=D:\software\Git\mingw64\libexec\git-core;D:\software\Git\mingw64\libexec\git-core;D:\software\Git\mingw64\bin;D:\software\Git\usr\bin;C:\Users\<USER>\bin;E:\message_auth\das_spider\venv\Scripts;D:\software\ShadowBot;D:\software\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python39;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\ShadowBot;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Users\<USER>\AppData\Local\ShadowBot\users\65235345841459200\apps\8f9ea900-a1c9-4b23-8a5f-2a43a396ee7f\venv\Lib\site-packages\adbutils\binaries;C:\Program Files\Java\jdk1.8.0_172\bin;C:\Program Files\Java\jdk1.8.0_172\jre\bin;D:\software\Git\cmd;E:\nodejs;C:\Users\<USER>\AppData\Local\Programs\Appium;C:\Users\<USER>\sdk\go1.22.2\bin;C:\Users\<USER>\go\bin;D:\software\frp_0.61.0_windows_amd64;C:\Program Files (x86)\ShadowBot;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\softwarenew\PyCharm Community Edition 2022.3.2\bin;D:\software\PyCharm 2;D:\software\ShadowBot;C:\Program Files (x86)\ShadowBot;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\softwarenew\PyCharm Community Edition 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin;D:\software\PyCharm 2022.3.3\bin;E:\nodejs\node_global;E:\nodejs;D:\software\Microsoft VS Code\bin;D:\software\PyCharm 2024.1.6\bin;D:\software\PyCharm Community Edition 2024.1.1\bin;D:\software\GoLand 2023.3.7\bin;D:\software\cursor\resources\app\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12904K (0% of 16119108K total physical memory with 639256K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
OS uptime: 1 days 11:18 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 2900, Current Mhz: 2900, Mhz Limit: 2900

Memory: 4k page, system-wide physical 15741M (624M free)
TotalPageFile size 28541M (AvailPageFile size 238M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 71M, peak: 317M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30) for windows-amd64 JRE (17.0.11+1-b1207.30), built on 2024-07-12 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

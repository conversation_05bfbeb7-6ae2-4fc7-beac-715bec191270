import datetime
import json
import random
import time
import re
import requests
import __init__
import models
from database import db, rds
from tm_comment.tm_trade_center import TmTradeCenter
from utils.logx import logger


LIMIT = 350

class TmOrderDecrypt:
    def __init__(self, username, password):
        self.cookie = ''
        self.username = username
        self.password = password

    def decrypt_order(self, start_time, end_time, tid=None, is_exists=None):
        if not self.get_cookie():
            return

        page_size = 10
        page_index = 0
        count = rds.hget(f"tb_{self.username}", "decrypt_count")
        if not count:
            count = 0
        count = int(count)
        while True:
            orders = self.list_order(tid, start_time, end_time, page_size, page_index, is_exists)
            if not orders:
                logger.info(start_time, end_time, page_size, "no orders")
                return
            for order_info in orders:
                if count >= LIMIT:
                    logger.info("达到当日获取上限")
                    return
                logger.info(order_info, count)
                order_id, order_time, order_address = order_info

                url = 'https://trade.tmall.com/detail/orderDetail.htm'
                params = {
                    "spm": 'a1z09.1.0.0.11ec3606oEeApI',
                    "bizOrderId": order_id,
                    'sifg': 1
                }
                # cookie = 'arms_uid=5b382176-69c6-42c6-8114-7534d4d80fd9; _bl_uid=9mkq8vvLuzmkkjnCk40j6y7wts9L; cna=TK+UGvwLz2gCATywcaRnYXQ6; sm4=330100; csa=0_0_0_0_0_0_0_0_0_0_0_0_0; ucn=center; cancelledSubSites=empty; t=4aa5db72d83a040c79fcbff2bbcdc6a5; enc=AX+6DvOUisgLAAAAAHPYedcB/f0L/f01/f00Ov39Mng4eEj9/f39B2Br/Sr9AXN267HkF6jrVrrbv32JsrpW2z7QhKBOO2eBpf8aTu5Qvg==; _tb_token_=734efbe55373e; cookie2=1017561abc515753be13ad6318de2806; lid=宝宝馋了旗舰店:嘉琛; xlly_s=1; sgcookie=E100Qn5QCP+lrfmdy5w3o8vEHqNDh5mqkCZlPQQzCixIcf1Z9vaB++zlsDLfcRyjGmMN5eCFIMfnL++I41x5Qratv9lqwT3R5Qi1ORLsfL3acxM=; uc1=cookie21=VFC/uZ9aj3yE&cookie14=UoewCLYwTmjYvA==; csg=b3ff1c9d; unb=2209789093064; sn=宝宝馋了旗舰店:嘉琛; _m_h5_tk=9b23716f1f23e6c10ce3129171669cf3_1648725579253; _m_h5_tk_enc=af8ff5ffc9d9e58c4acea425f8234069; l=eBPZMGVejWUL5XJDBOfwourza77OSIRfguPzaNbMiOCP9MCH5AlFW6VS-q8MCnGVnsh2R3lqBTYpBVYuMyz1lRPNJPyr1XASndLh.; isg=BNnZ_ehl3w1dtYEFzKkmCERQ6MOzZs0YyEB1d_uOVYB_AviUQ7OZ6eEQAMZ0umVQ; tfstk=cZilBImrkS1_4I4m53ZS3ZGRw3TOZ_SUsDoj0cItAoMLcliVimQV7NXkn7A6aw1..'
                cookie = self.cookie.encode('utf-8').decode('latin1')
                headers = {
                    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36",
                    "Cookie": cookie,
                    "referer": "https://trade.tmall.com/detail/orderDetail.htm?spm=a1z09.1.0.0.11ec3606couKXH&bizOrderId=%s" % order_id,
                }
                req = requests.request(method='GET', params=params, headers=headers, url=url)
                res = re.findall('var detailData =((?:.|\n)*?)</script>', req.text)
                if not res:
                    logger.error("re not result", res, '\n', req.text)
                    if req.text.find('扫一扫登录'):
                        if not self.get_cookie(refresh=True):
                            return
                        self.decrypt_order(tid, start_time, end_time, is_exists)
                    elif req.text.find('验证码'):
                        tc = TmTradeCenter(self.username, self.password)
                        if not self.get_cookie(refresh=True):
                            return
                        if not tc.order_manage():
                            return

                        self.decrypt_order(tid, start_time, end_time, is_exists)
                    return

                try:
                    j = json.loads(res[0].strip())
                except:
                    logger.error('json serialize error', order_id)

                    return

                for i in j.get("basic").get("lists"):
                    if i.get("key") != '收货地址':
                        continue
                    decrypt_data = dict()
                    content_text = i.get("content")[0].get("text").split(",")
                    content_len = len(content_text)
                    if content_len == 1:
                        content_text = content_text[0].split("，")
                        content_len = len(content_text)

                    if content_len == 3:
                        name, phone, original_address = content_text
                        postcode = None
                    elif content_len == 4:
                        name, phone, original_address, postcode = content_text
                    elif content_len == 5:
                        name, phone, second_phone, original_address, postcode = content_text
                        second_phone = second_phone.split('-', 1)[-1]
                        decrypt_data["second_phone"] = second_phone
                    else:
                        logger.error(i)
                        logger.error(i.get("content")[0].get("text").split(","))
                        raise Exception("len error")

                    phone = phone.split('-', 1)[-1]
                    decrypt_data["receiver_name"] = name
                    decrypt_data["receiver_tel"] = phone
                    decrypt_data["original_address"] = original_address
                    decrypt_data["do_time"] = datetime.datetime.now()
                    decrypt_data["tid"] = order_id
                    decrypt_data["order_time"] = order_time
                    decrypt_data["postcode"] = postcode
                    receiver_address = self.match_address(order_address, original_address)
                    if receiver_address:
                        decrypt_data["receiver_address"] = receiver_address
                    logger.info(name, phone, receiver_address, postcode)
                    decrypt_model = db.query(models.TmOrderDecryptRecord).filter_by(tid=order_id).first()
                    if decrypt_model:
                        self.update(decrypt_model, decrypt_data)
                    else:
                        self.create(decrypt_data)
                # logger.info(len(res))
                # page_index += 1
                count += 1
                rds.hset(f'tb_{self.username}', 'decrypt_count', count)
                t = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(1)
                rds.expireat(f'tb_{self.username}', t)
                time.sleep(random.randint(120, 150))

    def get_cookie(self, refresh=False):
        if refresh:
            if not TmTradeCenter(self.username, self.password).account_login():
                logger.error("cookie获取失败")
                return
        cookie_str = rds.hget(f"tb_{self.username}", "cookie")
        if not cookie_str:
            logger.info('no cookie')
            TmTradeCenter(self.username, self.password).account_login()
            cookie_str = rds.hget(f"tb_{self.username}", "cookie")

        cookie = json.loads(cookie_str)
        s = ""
        for k, v in cookie.items():
            s += k
            s += '='
            s += v
            s += ';'
        self.cookie = s
        return cookie

    @staticmethod
    def match_address(order_addr, original_address):
        receiver_addr = original_address.strip()[-(len(order_addr) + 1):].replace(" ", "", 1)
        for i in range(len(order_addr)):
            if order_addr[i] == '*':
                continue
            if order_addr[i] != receiver_addr[i]:
                return
        return receiver_addr

    @staticmethod
    def list_order(tid, start_time, end_time, page_size, page_index, is_exists=None):
        ret = db.query(models.CustTmOrderRecord.tid, models.CustTmOrderRecord.order_time, models.CustTmOrderRecord.receiver_address)
        ret = ret.join(models.TmOrderDecryptRecord, models.CustTmOrderRecord.tid == models.TmOrderDecryptRecord.tid, isouter=True)
        if not is_exists:
            ret = ret.filter(models.TmOrderDecryptRecord.id == None)
        ret = ret.filter(
            models.CustTmOrderRecord.receiver_tel != '',
            models.CustTmOrderRecord.receiver_tel.like('%*%')
            )
        if start_time:
            ret = ret.filter(models.CustTmOrderRecord.order_time >= start_time)
        if end_time:
            ret = ret.filter(models.CustTmOrderRecord.order_time <= end_time)
        if tid:
            ret = ret.filter(models.CustTmOrderRecord.tid == tid)
        ret = ret.order_by(models.CustTmOrderRecord.created_at.asc()).limit(page_size).offset(page_size*page_index)
        ret = ret.all()
        return ret

    @staticmethod
    def update(decrypt_model, decrypt_data):
        decrypt_data.pop("tid")
        decrypt_data.pop("order_time")
        for key, value in decrypt_data.items():
            setattr(decrypt_model, key, value)
        db.commit()

    @staticmethod
    def create(decrypt_data: dict):
        db.add(models.TmOrderDecryptRecord(**decrypt_data))
        db.commit()


if __name__ == '__main__':
    TmOrderDecrypt().decrypt_order("", "2022-03-30", "", True)
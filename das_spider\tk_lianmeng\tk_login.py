import datetime
import json
import random
import time
import traceback
import platform
import urllib3
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import Web<PERSON>river<PERSON>ait
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.common.keys import Keys
from lxml import etree
import __init__
from dateutil.parser import parse
from retrying import retry
from utils.logx import logger
from tm_comment.database import db, rds
from settings import config


def random_sleep(min_v: float = 1, max_v: float = 3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


class TkClientSpider:
    def __init__(self):
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')
        # chrome_options.add_argument('--disable-gpu')
        # chrome_options.add_argument("start-maximized")
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument(
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36")

        chrome_options.add_argument("--disable-blink-features")
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.browser = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.browser, 20)
        self.url = "https://www.alimama.com/member/login.htm"
        self.username = config.get("tb_account").get("username")
        self.password = config.get("tb_account").get("password")
        self.cookies = self.get_cookies()

    def complete(self, time_out) -> bool:
        end_time = time.time() + time_out
        while True:
            if self.browser.current_url == "https://www.alimama.com/index.htm":
                return True
            time.sleep(1)
            now = time.time()
            if now > end_time:
                break
        return False

    def account_login(self):
        self.browser.get(self.url)
        random_sleep(3, 5)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        self.browser.switch_to.frame(0)
        # 输入账号
        username_inp = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'fm-login-id'))
        )
        for i in self.username:
            username_inp.send_keys(i)
            random_sleep(0.3, 1.5)
        logger.info('账号已输入')
        random_sleep()
        # 输入密码
        password_inp = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//*[@id="fm-login-password"]'))
        )
        for i in self.password:
            password_inp.send_keys(i)
            random_sleep(0.3, 1.5)
        logger.info('密码已输入')
        random_sleep()

        # 登录框
        submit_but = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//button[@type="submit"]'))
        )
        submit_but.click()
        logger.info('点击登录按钮')
        if self.complete(20):
            logger.info('登录成功')
            self.save_cookies(self.browser.get_cookies())
            self.cookies = self.get_cookies()
            return True
        if self.browser.current_url.startswith("https://www.alimama.com/member/login.htm"):
            if self.check_login_status() or self.send_code():
                logger.info('登录成功')
                self.save_cookies(self.browser.get_cookies())
                self.cookies = self.get_cookies()
                return True
        logger.error("登录失败")
        return

    def check_login_status(self):
        self.browser.switch_to.frame(0)
        try:
            quick_login_but = self.wait.until(
                Ec.element_to_be_clickable((By.CLASS_NAME, 'fm-button'))
            )
            quick_login_but.click()
            return True
        except:
            return False

    def send_code(self):
        self.browser.switch_to.frame(0)
        for i in range(2):
            # 获取验证码
            get_code_but = self.wait.until(
                Ec.element_to_be_clickable((By.ID, 'J_GetCode'))
            )
            get_code_but.click()
            logger.info('获取验证码')
            # 验证码有效期15分钟
            for i in range(3):
                time.sleep(60*3)
                sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel = '%s' order by insert_time desc limit 1" % (time.strftime(
                                    "%Y-%m-%d", time.localtime()), "tk")
                cur = db.execute(sql)
                row = cur.fetchone()
                if not row:
                    logger.info("数据未查到")
                    continue
                res = row[0]
                send_code_inp = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="J_Phone_Checkcode"]'))
                )
                send_code_inp.send_keys(res)
                logger.info("%s验证码已输入" % res)
                time.sleep(1)

                submit_btn = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="submitBtn"]'))
                )
                submit_btn.click()
                logger.info("验证码已提交")
                time.sleep(3)

                if self.complete(10):
                    return True

    def save_cookies(self, cookies):
        cookies_dict = {}
        for i in cookies:
            cookies_dict[i.get("name")] = i.get("value")
        cookies_str = json.dumps(cookies_dict)
        rds.hset(f'tk_{self.username}', 'cookies', cookies_str)

    def get_cookies(self):
        cookies = rds.hget(f"tk_{self.username}", "cookies")
        if cookies:
            logger.info("get cookie is success")
            return json.loads(cookies)
        logger.info("get cookie is None")

    def __del__(self):
        self.browser.close()


if __name__ == '__main__':
    TkClientSpider().account_login()

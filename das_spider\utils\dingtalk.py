import datetime
import time
import hmac
import hashlib
import base64
import urllib.parse
from json import JSONDecodeError

import requests


def is_null_and_blank_str(content):
    """
    非空字符串
    :param content: 字符串
    :return: 非空 - True，空 - False
    >>> is_null_and_blank_str('')
    True
    >>> is_null_and_blank_str(' ')
    True
    >>> is_null_and_blank_str('  ')
    True
    >>> is_null_and_blank_str('123')
    False
    """
    if content and content.strip():
        return False
    else:
        return True


class DingTalk:
    def __init__(self, webhook, secret=""):
        """
        :param webhook: 钉钉机器人的Webhook地址
        :param secret: 如果是使用加签的方式，传入secret即可
        """
        self.webhook = webhook
        self.secret = secret
        self.times = 0
        self.start_time = time.time()
        self.headers = {'Content-Type': 'application/json; charset=utf-8'}

    def signing(self):
        """
        钉钉签名生成
        :param self:
        :return:
        """
        timestamp = str(round(time.time() * 1000))
        secret = self.secret
        secret_enc = secret.encode('utf-8')
        string_to_sign = '{}\n{}'.format(timestamp, secret)
        string_to_sign_enc = string_to_sign.encode('utf-8')
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return timestamp, sign

    def get_url(self):
        url = self.webhook
        if self.secret:
            timestamp, sign = self.signing()
            url += f"&timestamp={timestamp}&sign={sign}"
        return url

    def send_text(self, text:str, is_at_all=False, at_mobiles=[], at_dingtalk_ids=[]):
        """
        text类型
        :param msg: 消息内容
        :param is_at_all: @所有人时：true，否则为false（可选）
        :param at_mobiles: 被@人的手机号（可选）
        :param at_dingtalk_ids: 被@人的dingtalkId（可选）
        :return: 返回消息发送结果,true / false
        """
        if is_null_and_blank_str(text):
            print("text类型，内容不能为空")
            return

        data = {
            "msgtype": "text",
            "at": {},
            "text": {
                "content": text
            }
        }

        if is_at_all:
            data["at"]["isAtAll"] = is_at_all

        if at_mobiles:
            at_mobiles = list(map(str, at_mobiles))
            data["at"]["atMobiles"] = at_mobiles

        if at_dingtalk_ids:
            at_dingtalk_ids = list(map(str, at_dingtalk_ids))
            data["at"]["atDingtalkIds"] = at_dingtalk_ids

        return self.post(data)

    def send_markdown(self, title:str, markdown:str, is_at_all=False, at_mobiles=[], at_dingtalk_ids=[]):
        """
        markdown类型
        :param title: 首屏会话透出的展示内容
        :param text: markdown格式的消息内容
        :param is_at_all: 被@人的手机号（在text内容里要有@手机号，可选）
        :param at_mobiles: @所有人时：true，否则为：false（可选）
        :param at_dingtalk_ids: 被@人的dingtalkId（可选）
        :return: 返回消息发送结果, true / false
        """
        if is_null_and_blank_str(title) or is_null_and_blank_str(markdown):
            print("markdown类型，标题和内容不能为空")
            return

        data = {
            "msgtype": "markdown",
            "at": {},
            "markdown": {
                "title": title,
                "text": markdown
            }
        }

        if is_at_all:
            data["at"]["isAtAll"] = is_at_all

        if at_mobiles:
            at_mobiles = list(map(str, at_mobiles))
            data["at"]["atMobiles"] = at_mobiles

        if at_dingtalk_ids:
            at_dingtalk_ids = list(map(str, at_dingtalk_ids))
            data["at"]["atDingtalkIds"] = at_dingtalk_ids

        return self.post(data)

    def post(self, data):
        """
        发送消息（内容UTF-8编码）
        :param data: 消息数据（字典）
        :return: 返回发送结果
        """
        self.times += 1
        if self.times % 20 == 0:
            if time.time() - self.start_time < 60:
                print('钉钉官方限制每个机器人每分钟最多发送20条，当前消息发送频率已达到限制条件，休眠一分钟')
                time.sleep(60)
            self.start_time = time.time()

        url = self.get_url()
        try:
            req = requests.request(method="POST", headers=self.headers, url=url, json=data)
        except requests.exceptions.HTTPError as exc:
            print("消息发送失败， HTTP error: %d, reason: %s" % (exc.response.status_code, exc.response.reason))
            return
        except requests.exceptions.ConnectionError:
            print("消息发送失败，HTTP connection error!")
            return
        except requests.exceptions.Timeout:
            print("消息发送失败，Timeout error!")
            return
        except requests.exceptions.RequestException:
            print("消息发送失败, Request Exception!")
            return
        else:
            try:
                result = req.json()
            except JSONDecodeError:
                print("服务器响应异常，状态码：%s，响应内容：%s" % (req.status_code, req.text))
                return {'errcode': 500, 'errmsg': '服务器响应异常'}
            else:
                print('发送结果：%s' % result)
                if result['errcode']:
                    error_data = {"msgtype": "text", "text": {"content": "钉钉机器人消息发送失败，原因：%s" % result['errmsg']}, "at": {"isAtAll": True}}
                    print("消息发送失败，自动通知：%s" % error_data)
                    url = self.get_url()
                    requests.request(method="POST", headers=self.headers, url=url, json=error_data)
                    return False
                return True

if __name__ == '__main__':
    webhook = "https://oapi.dingtalk.com/robot/send?access_token=db3a766b54290f1a0d5f2b4840995a7de236d62454bf738306e95667f0c535ae"
    secret = "SEC494858f7682a919b24a7121e3fd951845a33c1ff540951090313ab40070b6544"
    DingTalk(webhook, secret).send_text("123")
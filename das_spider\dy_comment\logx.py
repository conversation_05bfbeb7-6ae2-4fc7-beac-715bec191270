import inspect
import os
import logging.config
import platform

import time

standard_format = '[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s]' \
                  '[%(levelname)s] %(message)s'
simple_format = '[%(levelname)s][%(asctime)s] %(message)s'
id_simple_format = '[%(levelname)s][%(asctime)s] %(message)s'

logfile_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
logfile_dir = os.path.join(logfile_dir, 'log')
dir_time = time.strftime('%Y-%m-%d', time.localtime())

if not os.path.isdir(logfile_dir):
    os.mkdir(logfile_dir)

logfile_path_dic = {
    logging.INFO: os.path.join(logfile_dir, "info_%s.log" % dir_time),
    logging.WARNING: os.path.join(logfile_dir, "warning_%s.log" % dir_time),
    logging.ERROR: os.path.join(logfile_dir, "error_%s.log" % dir_time),
}

LOGGING_DIC = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': standard_format
        },
        'simple': {
            'format': simple_format
        },
    },
    'filters': {},
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'info': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.INFO],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        },
        'warning': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.WARNING],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.ERROR],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        }
    },
    'loggers': {
        'DEBUG': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'INFO': {
            'handlers': ['info', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'WARNING': {
            'handlers': ['warning', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'ERROR': {
            'handlers': ['error', 'console'],
            'level': 'ERROR',
            'propagate': False,
        }
    },
}


class Logx:
    def __init__(self):
        self.__logger = {}
        logging.config.dictConfig(LOGGING_DIC)
        for k, v in LOGGING_DIC.get("loggers").items():
            self.__logger[k] = logging.getLogger(k)

    def getLogMessage(self, message):
        '''
            自定义增加输出：来源文件名以及行号，格式为[代码记录] 信息
        '''
        frame, filename, line_no, function_name, code, unknow_field = inspect.stack()[2]
        cur_system = platform.system()
        if cur_system == "Windows":
            filename = filename.split("\\")[-1]
        else:
            filename = filename.split("/")[-1]
        return "[%s:%s:%s] %s" % (filename, line_no, function_name, message)

    def debug(self, msg):
        self.__logger["DEBUG"].debug(self.getLogMessage(msg))

    def info(self, msg):
        self.__logger["INFO"].info(self.getLogMessage(msg))

    def warning(self, msg):
        self.__logger["WARNING"].warning(self.getLogMessage(msg))

    def error(self, msg):
        self.__logger["ERROR"].error(self.getLogMessage(msg))


def load_my_logging_cfg():
    logger = Logx()
    logger.info('It works!')
    return logger


logger = load_my_logging_cfg()

if __name__ == '__main__':
    logger.info("123")
    logger.warning("123")
    logger.error("123")

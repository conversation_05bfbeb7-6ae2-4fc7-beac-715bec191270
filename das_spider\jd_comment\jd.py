import time

from selenium import webdriver
from selenium.webdriver.common.by import By
from urllib import request
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import WebDriverWait
import cv2  #  pip3 install opencv-python
from selenium.webdriver.common.action_chains import ActionChains
import random
import pyautogui
from selenium.common import exceptions

'''
driver = webdriver.Chrome()
driver.implicitly_wait(5)  # 设置隐式等待(每隔1s找一次元素，找到继续，没找到就再等，直到超过5s)
driver.get('https://passport.shop.jd.com/login/index.action')
driver.find_element(By.XPATH, '//*[@clstag="pageclick|keycount|index_login|2"]').click()
driver.switch_to.frame(driver.find_element(By.XPATH, '//*[@id="loginFrame"]'))  # 下面的账号密码按钮在ifram里，所以需要进入ifram
driver.find_element(By.XPATH, '//*[@id="loginname"]').send_keys('dhdh逗号')
driver.find_element(By.ID, 'nloginpwd').send_keys('mm12345dgdgfgfh')
driver.find_element(By.ID, 'paipaiLoginSubmit').click()
'''


def get_dis():
    bj_rgb = cv2.imread(bg_name)  # 读取背景图的rgb
    bj_gray = cv2.cvtColor(bj_rgb, cv2.COLOR_BGR2GRAY)  # 灰度处理

    small_rgb = cv2.imread(small_name, 0)  # 读取背景图的rgb
    res = cv2.matchTemplate(bj_gray, small_rgb, cv2.TM_CCOEFF_NORMED)  # 匹配滑块在背景图的位置
    lo = cv2.minMaxLoc(res)
    # print(lo[2][0])  # x轴距离
    return lo[2][0]


#    鼠标移动
def move2(x):
    slide = driver.find_element(By.XPATH, '//*[@class="JDJRV-slide-left"]')
    right = driver.find_element(By.XPATH, '//*[@class="JDJRV-slide-right"]')
    x1 = slide.location.get('x')
    y1 = slide.location.get('y')
    print(slide.location, ',kw_x = ', x1, ',kw_y = ', y1,right.location)
    print('x=', x, slide)
    pyautogui.moveTo(910, 387, duration=1.0)
    # pyautogui.dragTo(630+x+35, 408, duration=0.8, button='left', tween=pyautogui.easeInQuad)
    # pyautogui.mouseDown()
    # pyautogui.moveTo(x1+x, y1, duration=0.5)
    # pyautogui.mouseUp()
    # pyautogui.mouseDown()


chrome_options = webdriver.ChromeOptions()
chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
chrome_options.add_argument("--disable-blink-features")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_experimental_option('useAutomationExtension', False)
# chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
chrome_options.add_argument('--no-sandbox')
# chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--disable-dev-shm-usage')
driver = webdriver.Chrome(options=chrome_options)
driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {  # window.navigator.webdriver=undefined
  "source": """
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    })
  """
})
driver.maximize_window()  # 浏览器窗口最大化
wait = WebDriverWait(driver, 6)
driver.implicitly_wait(3)  # 设置隐式等待(每隔1s找一次元素，找到继续，没找到就再等，直到超过5s)
# driver.get('https://passport.jd.com/common/loginPage?from=pop_vender&regTag=2&ReturnUrl=https%3A%2F%2Fpassport.shop.jd.com%2Fswitch%2Froute%3FRoutingUrl%3Dhttps%253A%252F%252Faround.shop.jd.com%252Fmain%252Fapp')
driver.get('https://passport.shop.jd.com/login/index.action')
driver.find_element(By.XPATH, '//*[@clstag="pageclick|keycount|index_login|2"]').click()
driver.switch_to.frame(driver.find_element(By.XPATH, '//*[@id="loginFrame"]'))  # 下面的账号密码按钮在ifram里，所以需要进入ifram
time.sleep(1)
driver.find_element(By.XPATH, '//*[@id="loginname"]').send_keys('dhdh逗号')
time.sleep(2)
driver.find_element(By.ID, 'nloginpwd').send_keys('mm12345dgdgfgfh')
time.sleep(3)
driver.find_element(By.ID, 'paipaiLoginSubmit').click()  # 点击登陆
time.sleep(2)
bg_name = './img/big.png'
small_name = './img/small.png'


def get_tracks(distance):
    #  拿到移动轨迹，模仿人的滑动行为，先匀速加速后匀减速v = v0+at
    z = 0
    v = 0  # 初速度
    t = 0.3  # 单位时间0.3s来统计轨迹，即0.3s内的位移
    tracks = []  # 位移/轨迹列表，列表内的一个元素代表0.3s的位移
    current = 0  # 当前的位移
    mid = distance*4/5  # 到达mid值开始减速
    while current < distance:
        if current < mid:
            a = 2  # 加速度越小，单位时间的位移越小，模拟的轨迹就越多越详细
        else:
            a = -3

        v0 = v  # 初速度
        s = v0*t + 0.5*a*(t**2)  # 0.3s时间内的位移
        current += s  # 当前位置
        tracks.append(round(s))  # 添加到轨迹列表
        z += round(s)
        v = v0+a*t  # 速度已经到达v, 该速度作为下次的初速度
    return tracks, z


# slide 滑动滑块
def slide():
    ele_big = wait.until(Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-bigimg"]/img')))  # 背景图
    ele_small = wait.until(Ec.element_to_be_clickable((By.XPATH, '//*[@class="JDJRV-smallimg"]/img')))   # 小图（滑块图）
    src_big = ele_big.get_attribute("src")
    src_small = ele_small.get_attribute('src')
    request.urlretrieve(src_big, bg_name)  # 下载背景大图保存到本地
    request.urlretrieve(src_small, small_name)  # 下载滑块保存到本地
    x = get_dis()
    x = int(x * 281 / 360)  # 计算缩放比(281网页中图片的宽度，360背景图的实际宽度)
    y = x/5
    z = x-y*5
    print('x= ', x, ' y = ', y)
    ActionChains(driver).click_and_hold(ele_small).perform()
    time.sleep(1)
    i = 0
    while i < 5:
        if i == 4:
            y += 3
        ActionChains(driver).move_by_offset(xoffset=y, yoffset=0).perform()
        i += 1
        time.sleep(2)
    ActionChains(driver).move_by_offset(xoffset=-5+z, yoffset=0).perform()
    time.sleep(1.6)
    ActionChains(driver).move_by_offset(xoffset=4, yoffset=0).perform()
    time.sleep(1.8)
    '''
    ActionChains(driver).click_and_hold(ele_small).perform()
    ActionChains(driver).pause(1)
    if x > 20:
        ActionChains(driver).move_by_offset(xoffset=x - 20, yoffset=0).perform()
        ActionChains(driver).pause(1)
        ActionChains(driver).move_by_offset(xoffset=10, yoffset=0).perform()
    else:
        ActionChains(driver).move_by_offset(xoffset=x - 10, yoffset=0).perform()
    ActionChains(driver).pause(1.5)
    ActionChains(driver).move_by_offset(xoffset=15, yoffset=0).perform()
    time.sleep(2)
    ActionChains(driver).move_by_offset(xoffset=-random.randint(5, 6),  yoffset=0).perform()
    time.sleep(1.6)
    ActionChains(driver).move_by_offset(xoffset=1, yoffset=0).perform()
    time.sleep(1.8)
    '''
    try:
        ActionChains(driver).release(ele_small).perform()
    except exceptions.StaleElementReferenceException:
        ele_small = wait.until(Ec.element_to_be_clickable((By.XPATH, '//*[@class="JDJRV-smallimg"]/img')))  # 小图（滑块图）
        ActionChains(driver).release(ele_small).perform()


def slide2():
    ele_big = wait.until(Ec.element_to_be_clickable((By.XPATH, '//div/div[@class="JDJRV-bigimg"]/img')))  # 背景图
    ele_small = wait.until(Ec.element_to_be_clickable((By.XPATH, '//*[@class="JDJRV-smallimg"]/img')))  # 小图（滑块图）
    src_big = ele_big.get_attribute("src")
    src_small = ele_small.get_attribute('src')
    request.urlretrieve(src_big, bg_name)  # 下载背景大图保存到本地
    request.urlretrieve(src_small, small_name)  # 下载滑块保存到本地
    ActionChains(driver).click_and_hold(ele_small).perform()
    time.sleep(1)
    x = get_dis()
    x = int(x * 281 / 360)  # 计算缩放比(281网页中图片的宽度，360背景图的实际宽度)
    print('x= ', x)
    yd = 0
    if x > 10:
        x -= 10
        yd += 10
        ActionChains(driver).move_by_offset(xoffset=10, yoffset=0).perform()
        time.sleep(1)

    tracks, z = get_tracks(x)
    y = x-z
    print('x2= ', x, y, z)
    for track in tracks:
        yd += track
        ActionChains(driver).move_by_offset(xoffset=track, yoffset=0).perform()
    time.sleep(0.8)
    if y != 0:
        yd += y
        ActionChains(driver).move_by_offset(xoffset=y, yoffset=0).perform()
        time.sleep(0.5)

    ActionChains(driver).move_by_offset(xoffset=1, yoffset=0).perform()
    time.sleep(0.5)
    ActionChains(driver).move_by_offset(xoffset=6, yoffset=0).perform()
    ActionChains(driver).move_by_offset(xoffset=-7, yoffset=0).perform()
    ActionChains(driver).release(ele_small).perform()
    print('yd ', yd)


counter = 1
while counter <= 5:
    counter += 1
    # slide()

    # x = get_dis()
    # move2(x)
    #
    slide2()

    time.sleep(2)
    if driver.current_url != driver.current_url:
        print("登陆成功")
    else:
        print("登陆失败")
    time.sleep(2)

time.sleep(5)
print(driver.current_url)



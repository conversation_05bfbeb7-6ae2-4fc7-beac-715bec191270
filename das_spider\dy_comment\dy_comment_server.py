import json
import math
import os
import time

from requests_html import HTMLSession
import dy_comment_client
import comment_dto
from models import DyComment
from database import db
from logx import logger
from settings import config

def retry(func):
    """
    重试装饰器，当被装饰的函数，第一个返回值为false时，会重新调用该函数，重试超过3次会抛出异常
    :param func: 被装饰的函数
    :return: 被装饰函数的返回值
    """

    def wrapper(*args, **kwargs):
        retry_count = 0
        while retry_count < 3:
            retry_count += 1
            if retry_count > 1:
                logger.warning(f"调用{func.__name__}方法第{retry_count}次")
            res = func(*args, **kwargs)
            if res:
                if isinstance(res, list) and not res[0]:
                    continue
                return res
        logger.error(f"调用{func.__name__}方法第{retry_count}次")
        raise KeyboardInterrupt(f"{func.__name__}方法调用超出{retry_count}次")

    return wrapper


def paging_loop(func):
    def wrapper(*args, **kwargs):
        res, total = func(*args, **kwargs)
        while total > 0:
            args[1].page += 1
            # dy_comment_client.random_sleep(1, 3)
            res, total = func(*args, **kwargs)
        return res

    return wrapper


class DyServerSpider:
    def __init__(self):
        self.base_url = 'https://fxg.jinritemai.com'
        self.header = {
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36",
            "referfer": "https://fxg.jinritemai.com/ffa/g/comment?product_id=3498854807983240290",
        }
        self.cookie = self.get_cookie()
        self.session = HTMLSession()
        self.email = config.get("dy_account").get("email")
        self.password = config.get("dy_account").get("password")

    def get_param_token(self) -> None:
        pass

    def account_login(self) -> None:
        """
        使用邮箱登录
        :return:
        """
        url = "https://fxg-sso.jinritemai.com/account_login/v2/"
        session = HTMLSession()
        header = {
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36",
            "referer": "https://fxg.jinritemai.com/",
        }
        cookie = {
            "passport_csrf_token_default": "f59d1b7340dc42cdb438c20a3c6da787",
            "passport_csrf_token": "f59d1b7340dc42cdb438c20a3c6da787", "PHPSESSID": "c0c71fb81d3db3c533d0ab59e07eb43c",
            "PHPSESSID_SS": "c0c71fb81d3db3c533d0ab59e07eb43c",
            "ttwid": "1%7CsiA_TecU2xt_B4ajJoWjTGiqc1DnkhfK03QYjf7AuDk%7C1638863173%7Cda65f0985a5a69a51dcfef6ac25fe1cee1291962dcc745d95180b4d120cb7016",
            "sso_auth_status": "0b8e3a8f8871ceb6fb928a3994909832"}
        data = {
            "fp": "",
            "aid": "4272",
            "language": "zh",
            "account_sdk_source": "web",
            "mix_mode": "1",
            "service": "https://fxg.jinritemai.com/ffa/multiShop?login_type=__placeholder__&login_mode=1&next=&ignore_sub_auth=0&v2_login_way=1",
            "account": self.encrypt(self.email),
            "password": self.encrypt(self.password),
            "captcha_key": ""
        }

        rsp = session.request(method="POST", url=url, data=data, headers=header, cookies=cookie, verify=False)
        logger.info(rsp.text)

    def sync_comment(self, param) -> None:
        first_time = param.comment_time_from
        last_time = param.comment_time_to

        while last_time - first_time > 86400:
            param.page = 0
            param.comment_time_from = last_time - 86400
            param.comment_time_to = last_time
            self._get_comment(param)
            last_time -= 86400

        param.page = 0
        param.comment_time_from = first_time
        param.comment_time_to = last_time
        self._get_comment(param)

    def save_comment(self, data):
        for comment_body in data:
            # 写入mongo
            # mongo = MongoDB(db="data_access", collection="dy_comment")
            # col = mongo.find_one({"id": comment_body.get("id")})
            # if col:
            #     mongo.update_one({"id": comment_body.get("id")}, comment_body)
            # else:
            #     mongo.insert_one(comment_body)

            # 写入mysql
            comment_item = db.query(DyComment).filter_by(comment_id=comment_body.get("id")).first()
            if comment_item:
                self.merge_comment(comment_body, comment_item)
                db.commit()
            else:
                comment_item = DyComment()
                self.merge_comment(comment_body, comment_item)
                db.add(comment_item)
                db.commit()

    @retry
    @paging_loop
    def _get_comment(self, param) -> (bool, int):
        """
        获取评论数据
        1. 如身份过期会进行三次重试
        2. 会根据total进行分页
        3. 暂未对分页超出200的数据进行处理
        :param param:
        :return:
        """
        url = self.base_url + "/product/tcomment/commentList"
        params = {'page': param.page, 'pageSize': param.page_size, 'id': param.id, 'rank': param.rank,
                  'filter': param.filter,
                  'comment_time_from': param.comment_time_from - 60, 'comment_time_to': param.comment_time_to,
                  'appid': '1',
                  '__token': 'a7ae92a878d6fbb40a46cb1e2c93a214', '_bid': 'ffa_goods', '_lid': '373446735607',
                  '_signature': '_02B4Z6wo00f01ksIqCQAAIDDM8ZDIZy4K5JLDKyAAPN5B9lTspACGloZzR0GNQyyE08PY8zSJf82-w9ZH1Llnf1xDLAvh1iix-QOo-pYjQY7evkJc22Y5UFpsck3FNooXI6vO1v7YA0RUDH813'}

        rsp = self.session.request(method="get", url=url, params=params, cookies=self.cookie, verify=False,
                                   headers=self.header)
        if rsp.status_code != 200:
            logger.error(f"_get_comment函数，{url} 请求失败,状态码为{rsp.status_code}")
            raise KeyboardInterrupt(f"{url} 请求失败,状态码为{rsp.status_code}")
        # MongoDB(db="data_access", collection="dy_record").insert_one(rsp.json())
        if rsp.json().get("code") in ("10008", "10002"):
            self.cookie = self.get_cookie(refresh=True)
            return False, 0
        elif rsp.json().get("code") != 0 and not rsp.json().get("data"):
            logger.error(f"_get_comment函数，数据获取失败,返回结果为{rsp.json()}")
            return False, 0
        data = rsp.json().get("data")
        total = rsp.json().get("total")
        logger.info(
            f'请求时间为: start_time {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(param.comment_time_from)) if param.comment_time_from else ""} end_time {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(param.comment_time_to)) if param.comment_time_to else ""},'
            f'请求第{param.page}/{math.ceil(total / 50)}页\n状态为{rsp.status_code},数据总量为{len(data)}')
        # for i in data:
        #     logger.info(i)

        if data and len(data) > 0:
            self.save_comment(data)
        else:
            total = 0

        return True, total

    @staticmethod
    def merge_comment(comment_body, comment_item):
        field_map = {
            "id": "comment_id",
        }
        static_map = {
            "photos": "photos_url",
            "videos": "videos_main_url",
        }
        datetime_map = {
            "comment_time": "comment_time_datetime",
            "create_time": "create_time_datetime",
            "reply_time": "reply_time_datetime"
        }

        if comment_body.get("product"):
            comment_item.product_name = comment_body.get("product").get("name")
            comment_item.product_img = comment_body.get("product").get("img")
        if comment_body.get("tags"):
            if comment_body.get("tags").get("rank_info"):
                comment_item.tags_rank_info_rank_tag = comment_body.get("tags").get("rank_info").get("rank_tag")
                comment_item.tags_rank_info_name = comment_body.get("tags").get("rank_info").get("name")
            if comment_body.get("tags").get("negative_tags"):
                tags_name = ""
                tags_desc = ""
                for tag in comment_body.get("tags").get("negative_tags"):
                    tags_name = tags_name + tag.get("name") + ","
                    tags_desc = tags_desc + tag.get("desc") + ","
                tags_name = tags_name.strip(",")
                tags_desc = tags_desc.strip(",")
                comment_item.tags_negative_tags_name = tags_name
                comment_item.tags_negative_tags_desc = tags_desc
        if comment_body.get("appends") and len(comment_body.get("appends")) > 0:
            append_info = comment_body.get("appends")[0]
            comment_time = append_info.get("comment_time")
            photos = append_info.get("photos")
            videos = append_info.get("videos")
            content = append_info.get("content")
            if comment_time:
                comment_item.appends_comment_time = time.strftime('%Y-%m-%d %H:%M:%S',
                                                                  time.localtime(comment_time))
            if photos and len(photos) > 0:
                photos_url = ""
                for photo in photos:
                    photos_url += photo.get("url")
                    photos_url += ","
                photos_url = photos_url.strip(",")
                comment_item.appends_photos_url = photos_url
            if videos and len(videos) > 0:
                videos_main_url = ""
                for video in videos:
                    videos_main_url += video.get("main_url")
                    videos_main_url += ","
                videos_main_url = videos_main_url.strip(",")
                comment_item.appends_videos_main_url = videos_main_url
            comment_item.appends_content = content

        for k, v in comment_body.items():
            col = k
            data = v
            if k in field_map:
                col = field_map.get(k)
            elif k in static_map:
                col = static_map.get(k)
                data = ""
                if not v:
                    v = []
                for static in v:
                    if static.get(col.lstrip(k + "_")):
                        data += static.get(col.lstrip(k + "_"))
                        data += ","
                data = data.strip(",")
            elif k in datetime_map:
                if v:
                    data = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(v))
                else:
                    data = None

            if hasattr(comment_item, col):
                setattr(comment_item, col, data)

        return comment_item

    @staticmethod
    def encrypt(e):
        t = []
        n = []

        def func(e):
            r = []
            for o in range(len(e)):
                t = 0
                n = e
                t = ord(e[o])
                r.append(t) if 0 <= t and t <= 127 else (
                    r.append(192 | 31 & t >> 6), r.append(128 | 63 & t)) if 128 <= t and t <= 2047 else (
                                                                                                                2048 <= t and t <= 55295 or 57344 <= t and t <= 65535) and (
                                                                                                            r.append(
                                                                                                                224 | 15 & t >> 12),
                                                                                                            r.append(
                                                                                                                128 | 63 & t >> 6),
                                                                                                            r.append(
                                                                                                                128 | 63 & t))
            for i in range(len(r)):
                r[i] &= 255
            return r

        t = func(e)
        for r in range(len(t)):
            n.append(str(hex(5 ^ t[r])).split("0x")[1])
        return "".join(n)

    @staticmethod
    @retry
    def get_cookie(refresh=False) -> map:
        if refresh:
            dy_comment_client.DyClientSpider().account_login()
        cookie_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        with open(cookie_path, mode="r", encoding="utf-8") as fr:
            data = fr.read()
            if data == "" or not json.loads(data):
                dy_comment_client.DyClientSpider().account_login()
                return
            cookie = json.loads(data)
        return cookie


if __name__ == '__main__':
    comment_param = comment_dto.CommentParam(page=201)
    dy = DyServerSpider()
    for i in range(1):
        # dy.sync_comment(comment_param)
        dy._get_comment(comment_param)
        time.sleep(3)



def match_address(order_addr, original_address):
    receiver_addr = original_address.strip()[-(len(order_addr) + 1):].replace(" ", "", 1)
    print(receiver_addr)
    for i in range(len(order_addr)):
        if order_addr[i] == '*':
            continue
        if order_addr[i] != receiver_addr[i]:
            return
    return receiver_addr

if __name__ == '__main__':
    sen_address = r'玉*镇干**道剑桥春天'
    full_address = '江西省 上饶市 余干县 玉亭镇 干越大道****'
    print(match_address(sen_address, full_address))
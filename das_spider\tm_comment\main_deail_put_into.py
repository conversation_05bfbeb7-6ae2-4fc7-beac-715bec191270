# encoding=utf-8
import concurrent.futures
import sys
import pandas as pd
import __init__
import database
from tm_comment.models import TianmaoComment

db = database.create_app()


class TMCommentInto:

    def __init__(self, file_name):
        self.file_name = file_name

    @staticmethod
    def split_list_by_n_loop(lst, n):
        new_lists = []
        for i in range(0, len(lst), n):
            # 切片操作，每次取n个元素
            new_lists.append(lst[i:i + n])
        return new_lists

    def dispose_data(self):
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)
        tasks = []

        df = pd.read_excel(self.file_name)
        df = df.fillna('Null')

        dict_list = df.to_dict(orient='records')
        split_list = self.split_list_by_n_loop(dict_list, 100)
        for rows in split_list:
            future = executor.submit(self.save_comment_data, rows)
            tasks.append(future)

        concurrent.futures.wait(tasks)

    def save_comment_data(self, rows):
        print("开始处理数据")
        self_db = database.create_app()
        for row in rows:
            # 删除空值
            for key, value in list(row.items()):
                if value == "Null":
                    del row[key]
            comment_id = row.get("first_comment_id")
            comment_item = self_db.query(TianmaoComment).filter_by(first_comment_id=comment_id).first()
            if comment_item:
                self._format_data(row, comment_item)
            else:
                item = TianmaoComment()
                self._format_data(row, item)
                self_db.add(item)
        self_db.commit()

    @staticmethod
    def _format_data(comment_dto, comment_model):
        comment_model.first_comment_id = comment_dto.get("first_comment_id")
        comment_model.first_comment = comment_dto.get("first_comment")
        comment_model.first_comment_time = comment_dto.get("first_comment_time")
        comment_model.first_comment_label_one = comment_dto.get("first_comment_label_one")
        comment_model.first_comment_label_two = comment_dto.get("first_comment_label_two")
        comment_model.user_level = comment_dto.get("user_level")
        comment_model.order_id = comment_dto.get("order_id")
        comment_model.good_id = comment_dto.get("good_id")
        comment_model.good_name = comment_dto.get("good_name")
        comment_model.sku_info = comment_dto.get("sku_info")
        comment_model.do_time = comment_dto.get("do_time")
        comment_model.remark = comment_dto.get("remark")
        comment_model.first_callback = comment_dto.get("first_callback")
        comment_model.first_callback_time = comment_dto.get("first_callback_time")
        comment_model.first_callback_name = comment_dto.get("first_callback_name")
        comment_model.first_comment_pngs = comment_dto.get("first_comment_pngs")
        comment_model.append_comment = comment_dto.get("append_comment")
        comment_model.append_comment_time = comment_dto.get("append_comment_time")
        comment_model.append_comment_label_one = comment_dto.get("append_comment_label_one")
        comment_model.append_comment_label_two = comment_dto.get("append_comment_label_two")
        comment_model.append_callback_time = comment_dto.get("append_callback_time")
        comment_model.append_callback_name = comment_dto.get("append_callback_name")
        comment_model.append_comment_pngs = comment_dto.get("append_comment_pngs")


if __name__ == '__main__':
    filename = sys.argv[2]
    # filename = r"D:\shadow_file\20240903\天猫评论数据-2024-08-24-2024-09-02-[20240903154842].xlsx"
    tm = TMCommentInto(filename)
    tm.dispose_data()

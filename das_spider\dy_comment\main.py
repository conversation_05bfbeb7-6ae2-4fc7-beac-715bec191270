import datetime

from comment_dto import CommentParam
from dy_comment_server import <PERSON>yServer<PERSON><PERSON><PERSON>


def run():
    now = datetime.datetime.now()
    end_time = str(now).split(".")[0]
    start_time = str(now - datetime.timedelta(days=90)).split(".")[0]
    # comment_param = CommentParam(comment_time_from="2021-12-28", comment_time_to="2021-12-29")
    comment_param = CommentParam(comment_time_from=start_time, comment_time_to=end_time)
    dy = DyServerSpider()
    dy.sync_comment(comment_param)


if __name__ == '__main__':
    run()

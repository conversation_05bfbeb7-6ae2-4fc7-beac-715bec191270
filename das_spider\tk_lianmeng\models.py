import time
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, Float, String, Text, ForeignKey, DateTime, UniqueConstraint, Index, event, TIMESTAMP, DECIMAL, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, LONGTEXT, MEDIUMTEXT, SMALLINT, TEXT, TINYINT, VARCHAR


_Base = declarative_base()


class BaseModel(_Base):
    __abstract__ = True

    id = Column(Integer(), primary_key=True)  # id 主键，默认自增，可以通过autoincrement参数设置
    created_at = Column(DateTime(), comment="创建时间")
    updated_at = Column(DateTime(), comment="更新时间")
    deleted_at = Column(DateTime(), comment="删除时间")
    remark = Column(String(55), comment="备注")


class TKGoodsAnalyseReport(BaseModel):
    __tablename__ = 'tk_goods_analyse_report'
    __table_args__ = {'comment': '淘宝客商品分析报表'}

    the_date = Column(VARCHAR(55), index=True, comment='日期')
    item_title = Column(VARCHAR(255), comment='商品名')
    item_id = Column(VARCHAR(55), comment='商品ID')
    item_url = Column(VARCHAR(255), comment='商品图片')
    auction_url = Column(VARCHAR(255), comment='商品链接')
    pre_commission_fee = Column(Float(), comment='付款佣金支出(元)')
    pre_commission_rate = Column(Float(), comment='付款佣金率')

    pre_service_fee = Column(Float(), comment='付款服务费支出')
    pre_total_fee = Column(Float(), comment='付款支付费用')
    pre_service_rate = Column(Float(), comment='付款服务费率')
    cm_service_fee = Column(Float(), comment='结算服务费支出')
    cm_service_rate = Column(Float(), comment='结算服务费率')
    cm_commission_fee = Column(Float(), comment='结算佣金支出')
    cm_commission_rate = Column(Float(), comment='结算佣金率')
    cm_total_fee = Column(Float(), comment='结算支出费用')

    alipay_amt = Column(Float(), comment='付款金额(元)')
    alipay_num = Column(INTEGER(), comment='付款笔数')
    coupon_dis_rate = Column(Float(), comment='商品折扣率')
    enter_shop_pv_tk = Column(INTEGER(), comment='进店量')
    clt_add_itm_cnt = Column(INTEGER(), comment='收藏宝贝量')
    cart_add_itm_cnt = Column(INTEGER(), comment='添加购物车量')

    def __repr__(self):
        return self.id.__str__()

    def _declarative_constructor(self, **kwargs):
        """A simple constructor that allows initialization from kwargs.
        Sets attributes on the constructed instance using the names and
        values in ``kwargs``.
        Only keys that are present as
        attributes of the instance's class are allowed. These could be,
        for example, any mapped columns or relationships.
        """
        cls_ = type(self)
        for k in kwargs:
            if not hasattr(cls_, k):
                raise TypeError("%r is an invalid keyword argument for %s" % (k, cls_.__name__))
            setattr(self, k, kwargs[k])

    # _declarative_constructor.__name__ = '__init__'


@event.listens_for(TKGoodsAnalyseReport, 'before_insert')
def receive_before_create(mapper, connection, target):
    "listen for the 'before_insert' event"
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(TKGoodsAnalyseReport, 'before_update')
def receive_before_update(mapper, connection, target):
    "listen for the 'before_update' event"
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')
import json
import os

import requests
import time
import datetime
# from mongo import MongoDB
from comment_model import db, JDComment, cache
from sqlalchemy.dialects.mysql import insert
from comment_dto import format_timestamp, timestamp_str
# import jd_login
from logx import logger
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def retry(func):
    """
    重试装饰器，当被装饰的函数，第一个返回值为false时，会重新调用该函数，重试超过3次会抛出异常
    :param func: 被装饰的函数
    :return: 被装饰函数的返回值
    """
    def wrapper(*args, **kwargs):
        retry_count = 0
        while retry_count < 3:
            retry_count += 1
            if retry_count > 1:
                logger.warning('retry method {} call count {}'.format(func.__name__, retry_count))
            res = func(*args, **kwargs)
            if res:
                if isinstance(res, list) and not res[0]:
                    continue
                return res
        logger.error('retry method {} call count {}'.format(func.__name__, retry_count))
        raise KeyboardInterrupt(f"{func.__name__}方法调用超出{retry_count}次")

    return wrapper


#  京东评论获取
class JdComment:
    def __init__(self, shop_name='宝宝馋了京东店'):
        # self.mg = MongoDB(db="data_access", collection="jd_comment_record")
        # self.mg2 = MongoDB(db="data_access", collection="jd_comment")
        # print('cookies=', self.cookies)
        self.mysql = db
        self.cache = cache
        # self.get_cookies(False)
        self.cookies = self.get_redis_cookies(shop_name)
        self.total = 0
        self.shop_name = shop_name
        # print('now', datetime.datetime.now())

    def update_mgo(self, item):
        res = self.mg2.find_one({'nid': item.get('nid')})
        if res:
            item['_id'] = res['_id']
            self.mg2.update_one({'nid': item.get('nid')}, item)
        else:
            self.mg2.insert_one(item)  # 添加到mongo

    @retry
    def get_cookies(self, refresh) -> bool:
        try:
            if refresh:
                input('请先登录京东账号，登录成功后按回车键继续')
                # jd = jd_login.JdLogin()
                # jd.login()
            cookies_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookies.json")
            fo = open(cookies_path, "r")
            cookies_str = fo.read()
            if cookies_str == '':
                fo.close()
                return self.get_cookies(True)
            fo.close()
            j = json.loads(cookies_str)
            try:
                self.cookies = j['cookies']
                return True
            except KeyError as err:
                logger.error('KeyError: {0}'.format(err))
                return False
        except FileNotFoundError as err:
            logger.error("error: {0}".format(err))
            return False
    
    def get_redis_cookies(self, shop_name):
        val = self.cache.hget("jd_cookie", shop_name)
        if val is None:
            raise Exception('cookie not found')
        cookie = json.loads(val).get("cookie", {})
        return cookie
    
    def save(self, items):
        if items is None:
            return
        self.total += len(items)
        print('len', len(items), 'total', self.total)
        # self.mysql.execute(JDComment.__table__.insert().values(items))
        # self.mysql.commit()
        # or
        # self.mysql.bulk_insert_mappings(JDComment, items)
        # self.mysql.commit()
        for item in items:
            i2 = item.copy()
            del i2['created_at']
            stmt = insert(JDComment).values(item).on_duplicate_key_update(i2)
            self.mysql.execute(stmt)
        self.mysql.commit()

    def send_request(self, begin_time, end_time, page=1, page_size=100, score=0, afterCommentFlag=None):
        """
        获取京东评论
        :param begin_time:
        :param end_time:
        :param page:
        :param page_size:
        :param score:
        :param afterCommentFlag:  是否有追评
        :return:
        """
        url = 'https://around.shop.jd.com/graphql'
        headers = {
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://around.shop.jd.com',
            'referer': 'https://around.shop.jd.com/main/app',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36'
        }
        data = {
            'query': 'query rateList($businessId: String,$venderId: Long!, $page: Int!, $pageSize: Int!, $replyPageSize: Int!, $orderIds: String, $skuids: String, $wareName: String, $keyword: String, $beginTime: String, $endTime: String, $pin: String,$nickName:String, $score: Int, $isVenderReply: Boolean, $isTop: String,$discussionFlag: Boolean,$afterCommentFlag: Boolean,$isDefaultComment: Boolean) { rateList(rateInput: {businessId: $businessId, venderId: $venderId, page: $page, pageSize: $pageSize, replyPageSize: $replyPageSize, orderIds: $orderIds, skuids: $skuids, wareName: $wareName, content: $keyword, beginTime: $beginTime, endTime: $endTime, pin: $pin,nickName:$nickName,  score: $score, isVenderReply: $isVenderReply, isTop: $isTop,discussionFlag: $discussionFlag, afterCommentFlag: $afterCommentFlag, isDefaultComment: $isDefaultComment}) { page totalItem comments { status pop_topped topped uid guid nid complainable complained skuId commentReplies { commentId content } replies { commentId nid content creationTime guid id nickName pin plusAvailable userClient  parentReply{ nickName } } skuId skuName wareImageUrls images { id imageTitle imgUrl } afterUserComment {content creationTime afterImages { id  imgUrl } }  orderId productColor productSize score discussionId isVenderReply isAppraise  pin nickName content creationTime replies { commentId content uid creationTime } replyCount md5code } } }',
            'variables': {
                'venderId': -1,
                'page': page,
                'pageSize': page_size,
                'replyPageSize': 30,
                'orderIds': None,
                'skuids': None,
                'endTime': end_time,
                'beginTime': begin_time,
                'keyword': None,
                'wareName': None,
                'nickName': None,
                'score': score,
                'isVenderReply': None,
                'isTop': None,
                'discussionFlag': None,
                'afterCommentFlag': afterCommentFlag,
                'isDefaultComment': None
            },
            'operationName': 'rateList'
        }
        logger.info(f"时间{begin_time} {end_time} 第{page}页")
        response = requests.post(url, headers=headers, json=data, verify=False, cookies=self.cookies)
        try:
            response_ = response.json()
            # self.mg.insert_one(response_)  # 添加到mongo
            # print('response_ ', response_, type(response_))
            # 解析
            data = response_.get('data')
            if data is None:
                err = response_.get('error')
                logger.error('requests err,  response {}, error {}, message{}'.format(response, err, response_.get('message')))
                return None, None, None, None
            if data:
                rate_list = data.get('rateList')
                page = rate_list.get('page')
                total_item = rate_list.get('totalItem')  # 总条数
                comments = rate_list.get('comments')
                items = []
                for comment in comments:
                    # self.update_mgo(comment)  # 更新mongo
                    item = JDComment()
                    item2 = {
                        'first_callback': None,
                        'first_callback_time': None,
                        'append_comment': None,
                        'append_comment_time': None,
                        'created_at': datetime.datetime.now(),
                        'updated_at': datetime.datetime.now()
                    }
                    for f, v in comment.items():
                        if hasattr(item, f):
                            # setattr(item, f, v)
                            item2[f] = v
                    rep = comment.get('replies')
                    images = ''
                    if comment.get('images') is not None:
                        for img in comment.get('images'):
                            imgurl = ("https:" + img['imgUrl']).replace("s40x40_", "")
                            if images != '':
                                images += ','+imgurl
                            else:
                                images = imgurl
                    item2['first_comment_img'] = images
                    after_comment = comment.get('afterUserComment')
                    after_images = ''
                    if after_comment is not None:
                        after_images2 = after_comment.get('afterImages')
                    if after_comment is not None and after_images2 is not None:
                        for img in after_images2:
                            imgurl = ("https:" + img['imgUrl']).replace("s40x40_", "")
                            if after_images != '':
                                after_images += ','+imgurl
                            else:
                                after_images = imgurl
                    item2['after_images'] = after_images
                    if rep is not None and len(rep) > 0:
                        item2['first_callback'] = rep[0]['content']
                        item2['first_callback_time'] = rep[0]['creationTime']
                    if after_comment is not None:
                        item2['append_comment'] = after_comment['content']
                        item2['append_comment_time'] = after_comment['creationTime']
                    item2['shop_name'] = self.shop_name
                    items.append(item2)
                    # print('item: ', item)
                self.save(items)
                logger.info('request,  result total_item {} page {}'.format(total_item, page))
                return page, page_size, score, total_item
        except Exception as err:
            # self.get_cookies(True)
            logger.error('request,  result {}, error{} page {}'.format(response, err, page))
            return None, None, None, None

    def sync_comment(self, start_time='', end_time='', afterCommentFlag=None) -> None:
        t1 = format_timestamp(start_time)
        t2 = format_timestamp(end_time)
        interval = 60*60*24*7
        if t2 - t1 > interval:
            tt1 = t1
            while tt1 < t2:
                t1 = tt1
                if t2 - tt1 > interval:
                    tt1 += interval
                else:
                    tt1 = t2
                start_time = timestamp_str(t1)
                end_time = timestamp_str(tt1)
                self.run(start_time=start_time, end_time=end_time, afterCommentFlag=afterCommentFlag)
                time.sleep(1)
        else:
            self.run(start_time=start_time, end_time=end_time, afterCommentFlag=afterCommentFlag)

    @retry
    def run(self, start_time='', end_time='', afterCommentFlag=None) -> bool:
        """
        同步京东评论
        :param start_time: 格式(YYYY-mm-dd HH:mm:ss)
        :param end_time: 格式(YYYY-mm-dd HH:mm:ss)
        :param afterCommentFlag: True获取有追评的评论
        :return:
        """
        logger.info('run start')
        page = 1
        if start_time == '':
            time1 = datetime.datetime.now()
            end_time = time1.strftime('%Y-%m-%d') + ' 00:00:00'
            start_time = (time1 - datetime.timedelta(days=1)).strftime('%Y-%m-%d') + ' 00:00:00'
        while True:
            request_page = page
            # logger.info('run start,  begin_time {}, end_time{}, page {}'.format(start_time, end_time, request_page))
            page, page_size, score, total_item = self.send_request(begin_time=start_time, end_time=end_time, page=request_page, page_size=100, score=0, afterCommentFlag=afterCommentFlag)
            if page:
                page += 1
                time.sleep(1)
                if page_size > total_item:
                    logger.info('run end,  begin_time= {}, end_time= {}, afterCommentFlag= {}'.format(start_time, end_time, afterCommentFlag))
                    logger.info('run finished')
                    return True
            else:
                return False


if __name__ == '__main__':
    hh = JdComment("京东自营")
    # hh.send_request(begin_time="2024-12-27 00:00:00", end_time="2024-12-27 23:59:59")  #  默认获取昨天的评论 start_time='2021-11-01 00:00:00', end_time='2021-12-15 00:00:00'
    hh.run(start_time="2024-12-27 00:00:00", end_time="2024-12-27 23:59:59")
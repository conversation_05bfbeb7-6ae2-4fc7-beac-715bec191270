import datetime
from decrypt import Tm<PERSON><PERSON>r<PERSON><PERSON>rypt
from concurrent.futures import Process<PERSON>oolExecutor
from settings import config


def today_run():
    username = config.get("tb_account").get("username")
    password = config.get("tb_account").get("password")
    start_time = '2022-05-01'
    end_time = '2022-05-02'
    # start_time = str(datetime.datetime.now() - datetime.timedelta(days=3)).split('.')[0]
    # end_time = str(datetime.datetime.now() - datetime.timedelta(hours=1)).split('.')[0]
    TmOrderDecrypt(username, password).decrypt_order(start_time, end_time)


def history_run():
    username = config.get("tb_account").get("username")
    password = config.get("tb_account").get("password")
    start_time = "2022-03-09"
    end_time = str(datetime.datetime.now() - datetime.timedelta(hours=1)).split('.')[0]
    TmOrderDecrypt(username, password).decrypt_order(start_time, end_time)


def run():
    pool = ProcessPoolExecutor(2)
    # pool.submit(today_run)
    pool.submit(history_run)

if __name__ == '__main__':
    run()

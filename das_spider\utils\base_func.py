import time


def ratio_util(current_num, lens, val, t0, *args):
    """
    打印循环进度
    @param current_num: 当前进度
    @param lens: 总长度
    @param val: 当前值
    @param t0: 循环开始时时间
    @param args: 额外打印字段
    @return:
    """
    plan_lens = 50
    s = "#"
    b = " "
    ratio = round(plan_lens * current_num / lens)
    process = round(current_num*100/lens, 2)
    elapsed = round(time.perf_counter() - t0, 2)
    print("\r{}第{}/{}次循环[{}{}]{}% 时间:{}s 值为:{}".format(*args, current_num, lens, s*ratio, (plan_lens-ratio)*b, process, elapsed, val), end='')
    if int(process) == 100:
        print()


def convert_to_hashable(obj):
    if isinstance(obj, dict):
        return tuple(sorted(obj.items()))
    elif isinstance(obj, tuple):
        return tuple(obj)
    else:
        return obj


def diff_inter(list1, list2):
    """
    两个list取交并差
    Args:
        list1 (list):[]
        list2 (list):[]
    Returns:
        intersection: list1和list2的交集
        union: list1和list2的并集
        difference1: list1和list2的差集
        difference2: list2和list1的差集
    """

    # def diff_inter_optimized(list1, list2):
    set1 = set(convert_to_hashable(obj) for obj in list1)
    set2 = set(convert_to_hashable(obj) for obj in list2)

    intersection = set1.intersection(set2)
    union = set1.union(set2)
    difference1 = set1.difference(set2)
    difference2 = set2.difference(set1)

    return list(intersection), list(union), list(difference1), list(difference2)


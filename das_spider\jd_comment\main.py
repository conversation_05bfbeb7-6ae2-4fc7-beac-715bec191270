import datetime
import sys

from comment import JdComment

if __name__ == '__main__':
    now = datetime.datetime.now()
    shop_name = sys.argv[2]
    start_time = sys.argv[3]
    end_time = sys.argv[4]
    hh = JdComment(shop_name)
    # end_time = str(now).split(".")[0]
    # %Y-%m-%d %H:%M:%S
    start_time = start_time+" 00:00:00"
    end_time = end_time+" 23:59:59"
    # start_time = str(now - datetime.timedelta(days=90)).split(".")[0]
    hh.sync_comment(start_time=start_time, end_time=end_time)
    # hh.sync_comment(start_time="2021-08-01 00:00:00", end_time="2021-12-02 00:00:00")  #  默认获取昨天的评论 start_time='2021-11-01 00:00:00', end_time='2021-12-15 00:00:00'

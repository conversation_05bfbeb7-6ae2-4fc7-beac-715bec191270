import time
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, UniqueConstraint, Index, event, TIMESTAMP, DECIMAL, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, LONGTEXT, MEDIUMTEXT, SMALLINT, TEXT, TINYINT, VARCHAR


_Base = declarative_base()


class BaseModel(_Base):
    __abstract__ = True

    id = Column(Integer(), primary_key=True)  # id 主键，默认自增，可以通过autoincrement参数设置
    created_at = Column(DateTime(), comment="创建时间")
    updated_at = Column(DateTime(), comment="更新时间")
    deleted_at = Column(DateTime(), comment="删除时间")
    remark = Column(String(55), comment="备注")


class TmOrderDecryptRecord(BaseModel):
    __tablename__ = 'tm_order_decrypt_record'
    __table_args__ = {'comment': '天猫订单解密数据'}

    tid = Column(VARCHAR(55), index=True, comment='订单号')
    order_time = Column(DateTime(), comment='下单时间')
    original_address = Column(VARCHAR(255), comment='原始地址')
    receiver_address = Column(VARCHAR(255), comment='解析地址')
    receiver_name = Column(VARCHAR(55), comment='姓名')
    receiver_tel = Column(VARCHAR(55), comment='手机号')
    do_time = Column(DateTime(), comment='执行时间')
    second_phone = Column(VARCHAR(55), comment='第二个电话')
    postcode = Column(VARCHAR(55), comment='邮编')

    def __repr__(self):
        return self.id.__str__()

    def _declarative_constructor(self, **kwargs):
        """A simple constructor that allows initialization from kwargs.
        Sets attributes on the constructed instance using the names and
        values in ``kwargs``.
        Only keys that are present as
        attributes of the instance's class are allowed. These could be,
        for example, any mapped columns or relationships.
        """
        cls_ = type(self)
        for k in kwargs:
            if not hasattr(cls_, k):
                raise TypeError("%r is an invalid keyword argument for %s" % (k, cls_.__name__))
            setattr(self, k, kwargs[k])

    # _declarative_constructor.__name__ = '__init__'


class CustTmOrderRecord(_Base):
    __tablename__ = 'cust_tm_order_record'

    id = Column(INTEGER(10), primary_key=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP)
    deleted_at = Column(TIMESTAMP)
    remark = Column(VARCHAR(255))
    tid = Column(VARCHAR(50), nullable=False, unique=True)
    shop_id = Column(VARCHAR(50), nullable=False)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    pay_amount = Column(DECIMAL(10, 2), nullable=False)
    balance_discount_amount = Column(DECIMAL(10, 2))
    discount_fee = Column(DECIMAL(10, 2))
    price = Column(DECIMAL(10, 2))
    status = Column(VARCHAR(50), nullable=False)
    type = Column(VARCHAR(50))
    order_time = Column(TIMESTAMP, nullable=False, index=True, server_default=text("'0000-00-00 00:00:00'"))
    pay_time = Column(TIMESTAMP, index=True)
    consign_time = Column(TIMESTAMP, index=True)
    finish_time = Column(TIMESTAMP)
    jdp_created = Column(TIMESTAMP)
    jdp_modifed = Column(TIMESTAMP)
    modified = Column(TIMESTAMP)
    new_presell = Column(TINYINT(1))
    close_type = Column(VARCHAR(50))
    order_items = Column(VARCHAR(255), nullable=False)
    receiver_province = Column(VARCHAR(255))
    receiver_city = Column(VARCHAR(255))
    receiver_district = Column(VARCHAR(255))
    receiver_address = Column(VARCHAR(255))
    receiver_name = Column(VARCHAR(50))
    receiver_tel = Column(VARCHAR(50))
    received_payment = Column(DECIMAL(10, 2), nullable=False)
    alipay_no = Column(VARCHAR(50))
    _from = Column('from', VARCHAR(50))
    outer_id = Column(VARCHAR(50), index=True)
    cust_id = Column(INTEGER(10))
    credit_card_fee = Column(DECIMAL(10, 2))
    receiver_town = Column(VARCHAR(255))
    receiver_zip = Column(VARCHAR(50))
    buyer_nick = Column(VARCHAR(50))
    buyer_area = Column(VARCHAR(50))
    buyer_email = Column(VARCHAR(50))
    receiver_country = Column(VARCHAR(50))
    buyer_obtain_point_fee = Column(INTEGER(8))
    buyer_rate = Column(TINYINT(1))
    adjust_fee = Column(DECIMAL(10, 2))
    available_confirm_fee = Column(DECIMAL(10, 2))
    platform_subsidy_fee = Column(DECIMAL(10, 2))
    trade_from = Column(VARCHAR(50))
    post_fee = Column(DECIMAL(10, 2))



@event.listens_for(TmOrderDecryptRecord, 'before_insert')
def receive_before_create(mapper, connection, target):
    "listen for the 'before_insert' event"
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(TmOrderDecryptRecord, 'before_update')
def receive_before_update(mapper, connection, target):
    "listen for the 'before_update' event"
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')
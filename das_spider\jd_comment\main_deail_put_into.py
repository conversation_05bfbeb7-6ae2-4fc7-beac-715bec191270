# encoding=utf-8
import datetime

import __init__
import concurrent.futures
import sys
import pandas as pd
from jd_comment.comment_model import db, JDComment, cache, init_db



class JDCommentInto:

    def __init__(self, file_name):
        self.file_name = file_name

    @staticmethod
    def split_list_by_n_loop(lst, n):
        new_lists = []
        for i in range(0, len(lst), n):
            # 切片操作，每次取n个元素
            new_lists.append(lst[i:i + n])
        return new_lists

    def dispose_data(self):
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)
        tasks = []

        df = pd.read_excel(self.file_name)
        df = df.fillna('Null')

        dict_list = df.to_dict(orient='records')
        split_list = self.split_list_by_n_loop(dict_list, 100)
        for rows in split_list:
            future = executor.submit(self.save_comment_data, rows)
            tasks.append(future)

        concurrent.futures.wait(tasks)

    def save_comment_data(self, rows):
        print("开始处理数据")
        self_db = init_db()
        for row in rows:
            # 删除空值
            for key, value in list(row.items()):
                if value == "Null":
                    del row[key]
            comment_id = row.get("nid")
            comment_item = self_db.query(JDComment).filter_by(nid=comment_id).first()
            if comment_item:
                comment_item.updated_at = datetime.datetime.now()
                self._format_data(row, comment_item)
            else:
                item = JDComment()
                item.created_at = datetime.datetime.now()
                item.updated_at = datetime.datetime.now()
                self._format_data(row, item)
                self_db.add(item)
        self_db.commit()

    @staticmethod
    def _format_data(comment_dto, comment_model):
        comment_model.nid = comment_dto.get("nid")
        comment_model.skuId = comment_dto.get("skuId")
        comment_model.orderId = comment_dto.get("orderId")
        comment_model.status = comment_dto.get("status")
        comment_model.content = comment_dto.get("content")
        comment_model.creationTime = comment_dto.get("creationTime")
        comment_model.first_comment_img = comment_dto.get("first_comment_img")
        comment_model.first_callback = comment_dto.get("first_callback")
        comment_model.first_callback_time = comment_dto.get("first_callback_time")
        comment_model.skuName = comment_dto.get("sku_name")
        comment_model.nickName = comment_dto.get("nickName")
        comment_model.append_comment = comment_dto.get("append_comment")
        comment_model.append_comment_time = comment_dto.get("append_comment_time")
        comment_model.after_images = comment_dto.get("after_images")
        comment_model.score = comment_dto.get("score")
        comment_model.pin = comment_dto.get("pin")
        comment_model.shop_name = comment_dto.get("shop_name")


if __name__ == '__main__':
    filename = sys.argv[2]
    # filename = r"D:\shadow_file\20250224\京东-评论数据-京东自营-2025-02-14-2025-02-23-[20250224161420].xlsx"
    jd = JDCommentInto(filename)
    jd.dispose_data()

import pymysql
import redis
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from settings import config

pymysql.install_as_MySQLdb()


def init_engine():
    engine = create_engine(
        "mysql+pymysql://%s" % config.get('database'),
        max_overflow=0,
        pool_size=5,
        pool_timeout=30,
        pool_recycle=-1,
        # echo=True
    )
    return engine


def create_app():
    engine = init_engine()
    return sessionmaker(bind=engine)()


def init_redis():
    pool = redis.ConnectionPool(host=config.get("redis").get("host"), port=6379, db=1)
    r = redis.Redis(connection_pool=pool)
    return r


db = create_app()
rds = init_redis()

import os
import json
import pandas as pd
import xlrd
import shutil
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, Float, DateTime, String, event
from models import TbSycmCompetesSell, TbSycmCompetesCustomer, TbSycmCompetesGoods
from settings import config

engine = create_engine(
        "mysql+pymysql://%s" % config.get('database'),
        max_overflow=0,
        pool_size=5,
        pool_timeout=30,
        pool_recycle=-1,
        # echo=True
    )
session = sessionmaker(bind=engine)()


def run():
    for root, dirs, files in os.walk("files"):
        for file in files:
            filename = os.path.join(root, file)
            print("start:", filename)
            if file.startswith("."):
                continue
            work_book = xlrd.open_workbook(filename)
            sheet_names = work_book.sheet_names()
            for sheet_name in sheet_names:
                # 仅支持处理销售、流量数据
                if sheet_name not in ("sell", "customer", "goods"):
                    continue
                dispose_file(filename, sheet_name)
            # 成功后，移动文件
            shutil.move(filename, os.path.join("used_files", file))
            print("end: ", filename)


def dispose_file(filename, sheet_name):
    df = pd.read_excel(filename, sheet_name=sheet_name, dtype=str)
    if df.empty:
        return
    with open(f"{sheet_name}_maps.json", "r", encoding="utf8") as fr:
        j = json.load(fr)
    df.rename(columns=j, inplace=True)
    df["date"] = df["date"].str.replace(" 00:00:00", "")
    # print(df)
    if sheet_name == "sell":
        table_name = "tb_sycm_competes_sell_tmp"
        table_instance = TbSycmCompetesSell
    elif sheet_name == "customer":
        table_name = "tb_sycm_competes_customer_tmp"
        table_instance = TbSycmCompetesCustomer
    elif sheet_name == "goods":
        table_name = "tb_sycm_competes_goods_tmp"
        table_instance = TbSycmCompetesGoods
    else:
        return
    # 入库临时表
    df.to_sql(table_name, engine, index=False, if_exists="append")
    try:
        # 没有就新增
        table_instance.insert_if_null(session)
        # 有就修改
        table_instance.update_if_exists(session)
        # 清空临时表
        table_instance.truncate_table(session)
    except Exception as e:
        table_instance.truncate_table(session)
        raise e


if __name__ == '__main__':
    run()

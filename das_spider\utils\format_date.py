import calendar
import datetime


def get_first_last_date(year:int, month:int) -> (str, str):
    """
    获取某年某月的第一天和最后一天

    """
    _, month_count_day = calendar.monthrange(year, month)
    start_day = str(datetime.date(year, month, day=1))
    end_day = str(datetime.date(year, month, day=month_count_day))
    return start_day, end_day


def is_natural_period(start_date, end_date, mode):
    """
    判断给定的日期范围是否是一个自然月或自然周。

    @param start_date: 开始日期的datetime对象
    @param end_date: 结束日期的datetime对象
    @param mode: 时间模式，可选值为 'week', 'month'
    @return: 如果是自然月或自然周，返回True；否则返回False
    """
    if mode == 'week':
        # 假设周一定义为每周的第一天
        start_weekday = start_date.weekday()
        end_weekday = end_date.weekday()
        # 检查开始日期是否是周一，结束日期是否是周日
        return start_weekday == 0 and end_weekday == 6 and (end_date - start_date).days == 6
    elif mode == 'month':
        # 检查开始日期是否是该月的第一天，结束日期是否是该月的最后一天
        _, last_day_of_month = calendar.monthrange(end_date.year, end_date.month)
        return start_date.day == 1 and end_date.day == last_day_of_month
    else:
        raise ValueError(f"Invalid mode: {mode}. Expected 'week' or 'month'.")


def get_previous_period(start_date_str, end_date_str, mode):
    """
    根据给定的时间和模式，返回前一天、前一周或前一个月的日期范围。

    @param start_date_str: 开始日期的字符串格式，如 'YYYY-MM-DD'
    @param end_date_str: 结束日期的字符串格式，如 'YYYY-MM-DD'
    @param mode: 时间模式，可选值为 'day', 'week', 'month'
    @return: 返回一个元组，包含开始日期和结束日期的字符串格式
    """
    # 将字符串转换为datetime对象
    start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d %H:%M:%S')
    end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d %H:%M:%S')
    new_start_date = None
    new_end_date = None
    if mode == 'day':
        new_start_date = start_date - datetime.timedelta(days=1)
        new_start_date_str = new_start_date.strftime('%Y-%m-%d')
        new_end_date_str = new_start_date+ + datetime.timedelta(hours=23, minutes=59, seconds=59)
        return new_start_date_str, new_end_date_str

    if not is_natural_period(start_date, end_date, mode):
        return Exception(
            f"检查日期是否是一个周期 {start_date_str} to {end_date_str}. mode:{mode} ")

        # 计算前一个月或前一周的日期范围
    if mode == 'week':
        # 找到本周一
        this_week_monday = start_date - datetime.timedelta(days=start_date.weekday())
        # 计算前一周的开始和结束日期
        new_start_date = this_week_monday - datetime.timedelta(days=7)
        new_end_date = new_start_date + datetime.timedelta(days=6, hours=23, minutes=59, seconds=59)
    elif mode == 'month':
        # 计算前一个月的第一天和最后一天
        prev_month = start_date.month - 1
        if prev_month == 0:
            prev_year = start_date.year - 1
            prev_month = 12
        else:
            prev_year = start_date.year

        _, last_day_of_prev_month = calendar.monthrange(prev_year, prev_month)
        new_start_date = datetime.datetime(prev_year, prev_month, 1)
        new_end_date = (datetime.datetime(prev_year, prev_month, last_day_of_prev_month) + 
                        datetime.timedelta(hours=23, minutes=59, seconds=59))

        # 将datetime对象转换回字符串格式
    new_start_date_str = new_start_date.strftime('%Y-%m-%d %H:%M:%S')
    new_end_date_str = new_end_date.strftime('%Y-%m-%d %H:%M:%S')

    return new_start_date_str, new_end_date_str

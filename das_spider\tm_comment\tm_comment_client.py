import datetime
import random
import time
import traceback
import platform
import urllib3
import os
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.common.keys import Keys
from lxml import etree
from dateutil.parser import parse
from retrying import retry
from logx import logger
from database import db
from models import TianmaoComment
from settings import config

urllib3.disable_warnings()


def random_sleep(min_v=1, max_v=3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


def retry_logging(exception):
    logger.error(exception)
    return True


def retry_if_result_false(result):
    return not result


class TmClientSpider:
    def __init__(self, start_date, end_date, comment_type='main'):
        """
            Init
        param start_date: 评论日期（起始）
        param end_date: 评论日期（截止）
        param comment_type: 评论类型，main：主评，append：追评。默认为main
        """
        chrome_options = webdriver.ChromeOptions()
        # 以下这些都是为了防止浏览器崩溃，而增加的一些属性
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        chrome_options.add_argument('--no-sandbox')  # 禁用沙盒，防止浏览器崩溃
        chrome_options.add_argument('--disable-dev-shm-usage')  # 不知道什么意思，也是防崩溃的
        # chrome_options.add_argument(
        #     "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36")
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')

        self.browser = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.browser, 20)  # 设置等待时常，如果在指定时间内未获取到元素，则报错
        self.url = 'https://voc.taobao.com/home.htm#'
        self.start_date = start_date
        self.end_date = end_date
        self.comment_type = comment_type
        self.username = config.get("tb_account").get("username")
        self.password = config.get("tb_account").get("password")

    def complete(self, time_out) -> bool:
        end_time = time.time() + time_out
        while True:
            if self.browser.current_url == 'https://voc.taobao.com/home.htm#/dashboard':
                return True
            time.sleep(1)
            now = time.time()
            if now > end_time:
                break
        return False

    # 天猫登录
    def tmall_account_login(self):
        self.browser.get("https://login.taobao.com/member/login.jhtml?sub=true&redirectURL=http%3A%2F%2Fvoc.taobao.com%2Fhome.htm#/")
        self.browser.delete_all_cookies()
        self.browser.add_cookie(
            # 这里不能写
            {'domain': '.login.taobao.com', 'httpOnly': True, 'name': 'lid', 'path': '/',
             'secure': True,
             'value': '%E5%AE%9D%E5%AE%9D%E9%A6%8B%E4%BA%86%E6%97%97%E8%88%B0%E5%BA%97%3A%E5%98%89%E7%90%9B'})  # 必须首先加载网站，这样Selenium 才能知道cookie 属于哪个网站
        url = 'https://login.tmall.com/?spm=a2186d.********.a2226mz.2.336e29c9PgfNxS&redirectURL=https%3A%2F%2Fvoc.taobao.com%2Fhome.htm%23%2Fdashboard'
        # 1. 请求网址
        self.browser.get(url)
        # 增加请求头，用户躲避滑动验证
        random_sleep(3, 5)

        self.browser.add_cookie(
            # 这里不能写
            {'domain': '.login.tmall.com', 'httpOnly': True, 'name': 'lid', 'path': '/',
             'secure': True, 'value': '%E5%AE%9D%E5%AE%9D%E9%A6%8B%E4%BA%86%E6%97%97%E8%88%B0%E5%BA%97%3A%E5%98%89%E7%90%9B'})  # 必须首先加载网站，这样Selenium 才能知道cookie 属于哪个网站
        self.browser.get(url)
        random_sleep(3, 5)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')
        self.browser.switch_to.frame(0)
        # 输入账号
        username = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'fm-login-id'))
        )
        username.send_keys(self.username)
        logger.info('账号已输入')
        random_sleep()
        # 输入密码
        password = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//*[@id="fm-login-password"]'))
        )
        password.send_keys(self.password)
        logger.info('密码已输入')
        random_sleep()
        input("请登录")

        # # 登录框
        # submit = self.wait.until(
        #     Ec.element_to_be_clickable((By.XPATH, '//button[@type="submit"]'))
        # )
        # submit.click()
        # logger.info('点击登录按钮')
        # if self.complete(20):
        #     logger.info('登录成功')
        #     return True
        # if self.browser.current_url.startswith('https://login.taobao.com/member/login_unusual.htm'):
        #     print(111)
        #     if self.send_code():
        #         logger.info('登录成功')
        #         return True
        # logger.error("登录失败")
        return

    # 淘宝登录
    # @retry(stop_max_attempt_number=3, wait_fixed=1000, retry_on_exception=retry_logging)
    def account_login(self):
        url = 'https://login.taobao.com/member/login.jhtml?sub=true&redirectURL=http%3A%2F%2Fvoc.taobao.com%2Fhome.htm#/'
        # 1. 请求网址
        self.browser.get(url)
        # 增加请求头，用户躲避滑动验证
        random_sleep(3, 5)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        # 输入账号
        username = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'fm-login-id'))
        )
        username.send_keys(self.username)
        logger.info('账号已输入')
        random_sleep()
        # 输入密码
        password = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//*[@id="fm-login-password"]'))
        )
        password.send_keys(self.password)
        logger.info('密码已输入')
        random_sleep()
        input("清登录")
        # 登录框
        # submit = self.wait.until(
        #     Ec.element_to_be_clickable((By.XPATH, '//button[@type="submit"]'))
        # )
        # submit.click()
        # logger.info('点击登录按钮')
        # if self.complete(20):
        #     logger.info('登录成功')
        #     return True
        # if self.browser.current_url.startswith('https://login.taobao.com/member/login_unusual.htm'):
        #     print(111)
        #     if self.send_code():
        #         logger.info('登录成功')
        #         return True
        # logger.error("登录失败")
        return True

    # @retry(stop_max_attempt_number=2, retry_on_result=retry_if_result_false)
    def send_code(self):
        self.browser.switch_to.frame(0)
        # self.wait = WebDriverWait(self.browser, 20)
        for i in range(2):
            # get_code_but = self.wait.until(
            #     Ec.element_to_be_clickable((By.XPATH, '//a[@id="otherValidator"]'))
            # )
            # get_code_but.click()
            #
            # get_code_but = self.wait.until(
            #     Ec.presence_of_element_located((By.CLASS_NAME, 'ui-button-morange"'))
            # )
            # get_code_but.click()
            # 获取验证码
            get_code_but = self.wait.until(
                Ec.element_to_be_clickable((By.ID, 'J_GetCode'))
            )
            get_code_but.click()
            logger.info('获取验证码')
            # 验证码有效期15分钟
            for i in range(3):
                time.sleep(60*3)
                sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel in ('%s', '%s') order by insert_time desc limit 1" % (time.strftime(
                                    "%Y-%m-%d", time.localtime()), "tm", 'test')
                cur = db.execute(sql)
                row = cur.fetchone()
                if not row:
                    logger.info("数据未查到")
                    continue
                res = row[0]
                send_code_inp = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="J_Phone_Checkcode"]'))
                )
                send_code_inp.send_keys(res)
                logger.info("%s验证码已输入" % res)
                time.sleep(1)

                submit_btn = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[@id="submitBtn"]'))
                )
                submit_btn.click()
                logger.info("验证码已提交")
                time.sleep(3)

                if self.complete(10):
                    return True

    # @retry(stop_max_attempt_number=3, wait_fixed=1000, retry_on_exception=retry_logging)
    def get_comment(self):
        self.select_option()
        first_time = parse(self.start_date)
        last_time = parse(self.end_date)
        while last_time >= first_time:
            do_time = last_time - datetime.timedelta(days=7)
            if do_time < first_time:
                do_time = first_time
            do_time_str = str(do_time).split()[0]
            last_time_str = str(last_time).split()[0]
            # 开始时间
            start_date_inp = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, 'input[placeholder="开始日期"]'))
            )
            if platform.system() == 'Darwin':
                start_date_inp.send_keys(Keys.COMMAND, 'a')
            else:
                start_date_inp.send_keys(Keys.CONTROL, 'a')
            random_sleep()
            start_date_inp.send_keys(do_time_str)
            random_sleep(3, 5)

            # 结束时间
            end_date_inp = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, 'input[placeholder="结束日期"]'))
            )
            if platform.system() == 'Darwin':
                end_date_inp.send_keys(Keys.COMMAND, 'a')
            else:
                end_date_inp.send_keys(Keys.CONTROL, 'a')
            end_date_inp.send_keys(last_time_str)
            random_sleep(3, 5)
            end_date_inp.send_keys(Keys.ENTER)
            random_sleep(3)

            count = 1
            # 翻页
            while True:
                logger.info(f'---{do_time} {self.comment_type} 第{count}次---')
                self.save_comment_data(self.browser.page_source)
                # 当没有数据时，页面会没有这个元素，所以需要额外处理一下
                try:
                    pagination_next_li = self.wait.until(
                        Ec.element_to_be_clickable((By.CSS_SELECTOR, '.ant-pagination-next'))
                    )
                except:
                    break
                # 判断是否还有下一页
                disable_type = pagination_next_li.get_attribute("aria-disabled")
                if disable_type == 'true':
                    break

                pagination_next_li.click()
                count += 1
                random_sleep(1, 2)

            last_time -= datetime.timedelta(days=8)

    def select_option(self):
        # 进入评价洞察
        evaluation_one_btn = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'a[href="#/feed-insight/dsr-analysis"]'))
        )
        evaluation_one_btn.click()
        random_sleep(2, 3)
        # 进入评价管理
        evaluation_two_btn = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'a[href="#/feed-insight/evaluation-management"]'))
        )
        evaluation_two_btn.click()
        random_sleep(2, 3)

        if self.comment_type == 'both':
            self.comment_type = 'main'
            self.get_comment()
            self.comment_type = 'append'

        if self.comment_type == 'append':
            # 选择追评
            submit = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, '.ant-select-selection-item[title="主评"]'))
            )
            submit.click()
            random_sleep()
            submit = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, '.ant-select-item-option[title="追评"]'))
            )
            submit.click()
            random_sleep()
        # try:
        #     # 有无内容
        #     submit = self.wait.until(
        #         Ec.element_to_be_clickable((By.CSS_SELECTOR, '.ant-checkbox-checked'))
        #     )
        #     submit.click()
        #     random_sleep()
        # except:
        #     pass

    def test(self):
        with open("./sound_code.txt", "r", encoding='utf8') as fr:
            sound_code = fr.read()
        self.save_comment_data(sound_code)

    def save_comment_data(self, data):
        selector = etree.HTML(data)
        cell_rows = selector.xpath('//tr[@data-row-key]')
        logger.info(f"有{len(cell_rows)}条数据")
        if len(cell_rows) != 20:
            logger.error(f"数据缺失，只有{len(cell_rows)}条")
        for row in cell_rows:
            comment_id = row.get("data-row-key")

            comment_item = db.query(TianmaoComment).filter_by(first_comment_id=comment_id).first()
            if comment_item:
                self._format_data(row, comment_item)
            else:
                item = TianmaoComment()
                self._format_data(row, item)
                db.add(item)
            db.commit()

    @staticmethod
    def _format_data(comment_dto, comment_model):
        comment_id = comment_dto.get("data-row-key")
        # kkk = i.xpath('.//div[@class="evaluation-cell"]')[0]
        comment_infos = comment_dto.xpath('.//div[@class="evaluation-wrapper"]')
        first_comment_info = comment_infos[0]
        first_comment_content = first_comment_info.xpath('.//div[@class="content"]')[0].text
        first_comment_time = first_comment_info.xpath('.//span[@class="createTime"]')[0].text
        first_comment_tags = first_comment_info.xpath('.//div[@class="tags"]/span')
        first_explain_content = first_comment_info.xpath('.//div[@class="explain"]/text()')
        first_explain_time = first_comment_info.xpath('.//span[@class="time"]')
        first_explain_name = first_comment_info.xpath('.//span[@class="nike"]')
        first_img_urls = first_comment_info.xpath('.//img[@class="ant-image-img"]')
        first_video_urls = first_comment_info.xpath('.//div[@class="video-list"]//div[@class="item"]')
        if len(comment_infos) == 2:
            append_comment_info = comment_infos[1]
            append_comment_content = append_comment_info.xpath('.//div[@class="content"]')[0].text
            append_comment_time = append_comment_info.xpath('.//span[@class="createTime"]')[0].text
            append_comment_tags = append_comment_info.xpath('.//div[@class="tags"]/span')
            append_explain_content = append_comment_info.xpath('.//div[@class="explain"]/text()')
            append_explain_time = append_comment_info.xpath('.//span[@class="time"]')
            append_explain_name = append_comment_info.xpath('.//span[@class="nike"]')
            append_img_urls = append_comment_info.xpath('.//img[@class="ant-image-img"]')
            append_video_urls = append_comment_info.xpath('.//div[@class="video-list"]//div[@class="item"]')

        # username = comment_dto.xpath('.//span[@class="name"]')[0].text
        user_level = comment_dto.xpath('.//img[@alt="买家等级"]')[0].get("src")
        order_id = comment_dto.xpath('.//span[@class="id"]/span')[1].text

        product_info = comment_dto.xpath('.//div[@class="product-cell"]/div[@class="text"]')[0]
        product_id = product_info.xpath('./div[@class="id"]')[0].text.split("：")[-1]
        product_name = product_info.xpath('./a')[0].text
        product_url = product_info.xpath('./a')[0].get("href")
        sku_name = product_info.xpath('./div[@class="skuInfo"]')[0].text
        comment_model.first_comment_id = comment_id
        if first_comment_content == '此用户没有填写评论！':
            first_comment_content = ''
        comment_model.first_comment = first_comment_content
        comment_model.first_comment_time = first_comment_time
        if len(first_comment_tags) == 1:
            comment_model.first_comment_label_one = first_comment_tags[0].text
        elif len(first_comment_tags) >= 2:
            comment_model.first_comment_label_one = first_comment_tags[0].text
            comment_model.first_comment_label_two = first_comment_tags[1].text
        # comment_model.username = username
        comment_model.user_level = user_level.strip("/")
        comment_model.order_id = order_id
        comment_model.good_id = product_id
        comment_model.good_name = product_name
        comment_model.sku_info = sku_name
        comment_model.do_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        comment_model.remark = '0304-push'
        if len(first_explain_content) > 0:
            comment_model.first_callback = first_explain_content[0]
            comment_model.first_callback_time = first_explain_time[0].text
            comment_model.first_callback_name = first_explain_name[0].text

        img_url = ""
        for img in first_img_urls:
            img_url += img.get("src").lstrip("/")
            img_url += ","
        if img_url:
            comment_model.first_comment_pngs = img_url

        # video_url = ""
        # for video in first_video_urls:
        #     video_url += video.get("style").split('"')[1].strip("/")
        #     video_url += ","
        # if video_url:
        #     comment_model.first_comment_video = video_url

        if len(comment_infos) == 2:
            comment_model.append_comment = append_comment_content
            comment_model.append_comment_time = append_comment_time
            if len(append_comment_tags) == 1:
                comment_model.append_comment_label_one = append_comment_tags[0].text
            elif len(append_comment_tags) >= 2:
                comment_model.append_comment_label_one = append_comment_tags[0].text
                comment_model.append_comment_label_two = append_comment_tags[1].text

            if len(append_explain_content) > 0:
                comment_model.append_callback = append_explain_content[0]
                comment_model.append_callback_time = append_explain_time[0].text
                comment_model.append_callback_name = append_explain_name[0].text

            img_url = ""
            for img in append_img_urls:
                img_url += img.get("src").lstrip("/")
                img_url += ","
            if img_url:
                comment_model.append_comment_pngs = img_url

            # video_url = ""
            # for video in append_video_urls:
            #     video_url += video.get("style").split('"')[1].strip("/")
            #     video_url += ","
            # if video_url:
            #     comment_model.append_comment_video = video_url

    def run(self):
        try:
            res = self.account_login()
            if not res:
                return
            self.get_comment()
            self.browser.quit()
            os.system("taskkill /f /im chromedriver.exe")  # 终止chromedriver进程
            os.system("taskkill /f /im chrome.exe")  # 终止chrome进程
            logger.info(f"{self.start_date} - {self.end_date} 获取结束")
        except:
            self.browser.quit()
            os.system("taskkill /f /im chromedriver.exe")  # 终止chromedriver进程
            os.system("taskkill /f /im chrome.exe")  # 终止chrome进程
            logger.error(f"{self.start_date} - {self.end_date} 获取失败")
            logger.error(traceback.format_exc())


if __name__ == '__main__':
    tm = TmClientSpider("2022-02-01", "2022-02-01", "both")
    tm.account_login()
    # tm.run()
    pass
    # tm.monitoring()
    # TmClientSpider().test()

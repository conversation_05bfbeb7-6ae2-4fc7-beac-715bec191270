<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="8">
            <item index="0" class="java.lang.String" itemvalue="2.7" />
            <item index="1" class="java.lang.String" itemvalue="3.5" />
            <item index="2" class="java.lang.String" itemvalue="3.6" />
            <item index="3" class="java.lang.String" itemvalue="3.7" />
            <item index="4" class="java.lang.String" itemvalue="3.8" />
            <item index="5" class="java.lang.String" itemvalue="3.9" />
            <item index="6" class="java.lang.String" itemvalue="3.10" />
            <item index="7" class="java.lang.String" itemvalue="3.11" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="django" />
            <item index="2" class="java.lang.String" itemvalue="pymysql" />
            <item index="3" class="java.lang.String" itemvalue="Flask-Cache" />
            <item index="4" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="5" class="java.lang.String" itemvalue="django-redis" />
            <item index="6" class="java.lang.String" itemvalue="wordcloud" />
            <item index="7" class="java.lang.String" itemvalue="django-simple-captcha" />
            <item index="8" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="9" class="java.lang.String" itemvalue="scrapy" />
            <item index="10" class="java.lang.String" itemvalue="Flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="Lab.Ex2.Program.UIimport" />
          <option value="Lab.Lab06.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>
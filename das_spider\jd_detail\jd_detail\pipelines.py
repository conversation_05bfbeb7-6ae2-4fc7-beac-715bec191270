# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import datetime

import pymysql
from jd_detail.settings import Mysql, SECRET_KEY
from jd_detail.utils.encryption_decryption import EncryptionDecryption


class JdMySQLPipeline:
    def __init__(self):
        en = EncryptionDecryption(SECRET_KEY)
        self.conn = pymysql.connect(  # 连接数据库
            host=Mysql['host'],
            port=Mysql['port'],
            user=Mysql['user'],
            password=en.aes_decryption(Mysql['password']),
            database=Mysql['database'],
            charset=Mysql['charset'],
        )
        self.cursor = self.conn.cursor()  # 获取游标

    def close_spider(self, spider):
        if self.conn:
            self.conn.close()

    def insert_jd_detail(self, goods_id, goods_url, sku_name, image_src, choose_item, specification, packing_list,product_status):
        """
        插入:京东爬取相关信息
        :param goods_id:商品名称,sku_name:sku名字,image_src:图片链接,choose_item:选择项目,created_at:创建时间
        """

        try:
            sql = '''INSERT INTO das_jd_competing_pair_info (goods_id,goods_url,sku_name, image_src, choose_item,specification,packing_list,product_status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s,%s)
               '''
            self.cursor.execute(sql, (
            goods_id, goods_url, sku_name, image_src, choose_item, specification, packing_list, product_status))
            self.conn.commit()
        except Exception as e:
            print("insert_jd_detail报错啦!", e)
            self.conn.rollback()

    def get_goods_url(self, collection_time, num_min):
        sql = '''
            SELECT id,goods_url FROM brand_tm_month_goods_info 
            WHERE deleted_at is null and channel = 'JD' and 
            (last_collection_time < %s OR last_collection_time is NULL) LIMIT %s,50
         '''
        self.cursor.execute(sql, (collection_time, num_min))
        list_header = [row[0] for row in self.cursor.description]
        list_result = [[str(item) for item in row] for row in self.cursor.fetchall()]
        res = [dict(zip(list_header, row)) for row in list_result]
        return res

    def update_collection_time(self, id):
        try:
            last_collection_time = datetime.datetime.now()
            sql = '''
                        UPDATE brand_tm_month_goods_info
                        SET last_collection_time = '%s'
                        WHERE id ='%s'
            ''' % (last_collection_time, id)

            self.cursor.execute(sql)
            self.conn.commit()
        except Exception as e:
            print("update_collection_time报错啦!", e)
            self.conn.rollback()

    def process_item(self, item, spider):
        try:
            self.insert_jd_detail(
                goods_id=item["goods_id"],
                goods_url=item["goods_url"],
                sku_name=item["sku_name"],
                image_src=item["img_src"],
                choose_item='|'.join(item["choose_list"]),
                specification=item["specification"],
                # specification='|'.join(item["specification"]),
                packing_list=item["packing_list"],
                product_status=item["product_status"]
            )
            self.update_collection_time(item["id"])
            print("插入并更新成功")
        except Exception as e:
            print(e)
        return item

    def get_goods_image(self):
        # 分页读取数据，使用yield生成器返回数据
        self.cursor.execute("SELECT id,image_src FROM das_jd_competing_pair_info where identify_info is null")
        while True:
            result = self.cursor.fetchone()
            if result:
                yield result
            else:
                break

    def update_jd_detail(self, jd_id, identify_info):
        """
        更新:京东爬取相关信息
        :param goods_id:商品名称,sku_name:sku名字,image_src:图片链接,choose_item:选择项目,created_at:创建时间
        """
        try:
            sql = '''UPDATE das_jd_competing_pair_info SET identify_info = %s WHERE id = %s
              '''
            self.cursor.execute(sql, (identify_info, jd_id))
            self.conn.commit()
        except Exception as e:
            print("update_jd_detail报错啦!", e)
            self.conn.rollback()

    def get_goods_text(self):
        sql = "SELECT id, identify_info FROM das_jd_competing_pair_info where identify_info is not null and identify_money is null;"
        self.cursor.execute(sql)
        return self.cursor.fetchall()

    def update_goods_price(self, id, identify_money, identify_mode):
        sql = '''UPDATE das_jd_competing_pair_info SET identify_money = %s, identify_mode = %s WHERE id = %s'''
        self.cursor.execute(sql, (identify_money, identify_mode, id))
        self.conn.commit()

    def close(self):
        """
        关闭数据库连接
        """
        self.conn.close()


if __name__ == '__main__':
    db = JdMySQLPipeline()
    #     goods_id = "10024525977797"
    #     sku_name = "宝宝馋了高钙虾皮食材干货儿童无添加盐新鲜淡干虾皮小虾米海产享宝宝食谱"
    #     img_src = 'https://img12.360buyimg.com/n1/jfs/t1/97226/30/25335/159371/6499c8ceF1454f27a/765e3524ba2539f2.jpg'
    #     choose_item = ''
    #     # db.insert_jd_detail(goods_id, sku_name, img_src, choose_item)
    a = db.get_goods_url("2023-06-29", 0)
    # res=db.update_collection_time(id="29154")
    print(a)
    db.close()

import requests

from jd_detail.pipelines import JdMySQLPipeline
from jd_detail.settings import SECRET_KEY,BAIDU_OCR_API_KEY ,BAIDU_OCR_SECRET_KEY
from jd_detail.utils.encryption_decryption import EncryptionDecryption


class BaiduOCR:

    def __init__(self):
        self.api_key = BAIDU_OCR_API_KEY
        self.secret_key = BAIDU_OCR_SECRET_KEY
        self.access_token = self._get_access_token()

    def _decryption_key(self):
        """
        解密
        """
        self.api_key = EncryptionDecryption(SECRET_KEY).aes_decryption(self.api_key)
        self.secret_key = EncryptionDecryption(SECRET_KEY).aes_decryption(self.secret_key)

    def _get_access_token(self):
        """
        使用 AK，SK 生成鉴权签名（Access Token）
        """
        self._decryption_key()
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {"grant_type": "client_credentials", "client_id": self.api_key, "client_secret": self.secret_key}
        return str(requests.post(url, params=params).json().get("access_token"))

    def ocr_image(self, image_url):
        """
        使用百度 OCR 识别图片中的文字
        
        :param image_url: 待解析的图片 url
        :return: 解析结果（json 字符串）
        """
        url = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token=" + self.access_token
        payload = 'url=' + image_url
        headers = {'Content-Type': 'application/x-www-form-urlencoded', 'Accept': 'application/json'}
        response = requests.request("POST", url, headers=headers, data=payload)
        return response.text


if __name__ == "__main__":
    image_url = "https://img13.360buyimg.com/n1/s450x450_jfs/t1/125056/21/39322/101993/64987fc7F4978d5c8/730f1a16ebb90763.jpg"

    ocr = BaiduOCR()
    result = ocr.ocr_image(image_url)
    print(result)
    # ocr.set_goods_identify_info()

.DS_Store
scripts
log.log
__pycache__
db.sqlite3
cpython-36.pyc
test/

# config
**/dev.json

# subproject
boss/*
!boss/.gitkeep
brand_data/*
!brand_data/.gitkeep
message_auth/*
!message_auth/.gitkeep
wdt_data/*
!wdt_data/.gitkeep
voc_yuyi/*
!voc_yuyi/.gitkeep
price_cat/*
!price_cat/.gitkeep
ali_code_review/*
!ali_code_review/.gitkeep
moojing_data/*
!moojing_data/.gitkeep
finance_data/*
!finance_data/.gitkeep
xhs_data/*
!xhs_data/.gitkeep
shadow_data/*
!shadow_data/.gitkeep
sycm_data/*
!sycm_data/.gitkeep

# local env files
.env.local
.env.*.local

# Log files
log/

# static resource
dy_comment/img/
jd_comment/img/

venv/

# koc file
koc/userfile/
koc/vcf_file/

# sycm file
tb_sycm_competes/files/*
!tb_sycm_competes/files/.gitkeep
tb_sycm_competes/used_files/*
!tb_sycm_competes/used_files/.gitkeep

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

import datetime
import time
import __init__
import redis
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, UniqueConstraint, Index
import pymysql
from sqlalchemy.orm import sessionmaker
from settings import config
from utils.encryption import AesEncryption


Base = declarative_base()
pymysql.install_as_MySQLdb()


class JDComment(Base):
    __tablename__ = "das_jd_comment"
    id = Column(Integer, primary_key=True)
    updated_at = Column(DateTime, nullable=False, default='NOW()', comment="更新时间")
    created_at = Column(DateTime, nullable=False, default='NOW()', comment="创建时间")
    nid = Column(String(50), unique=True, nullable=False, comment="评论id")
    skuId = Column(String(32), index=True, nullable=False, comment="商品id")
    orderId = Column(String(50), index=True, nullable=False, comment="订单id")
    status = Column(Integer, comment="评价状态")
    content = Column(String(255), comment="首次评论")
    creationTime = Column(DateTime, comment="首次评论时间")
    first_comment_img = Column(Text, comment="首次评论图片链接")
    first_callback = Column(String(255), comment="店家首次回复")
    first_callback_time = Column(DateTime, comment="店家首次回复时间")
    skuName = Column(String(100), comment="sku名称")
    nickName = Column(String(55), comment="用户昵称")
    append_comment = Column(String(255), comment="追加评论")
    append_comment_time = Column(DateTime, comment="追加评论时间")
    after_images = Column(Text, comment="追加评论图片链接")
    score = Column(Integer, comment="评价等级")
    pin = Column(String(32), comment="")
    discussionId = Column(String(32), comment="")
    pop_topped = Column(Integer, comment="")
    topped = Column(Integer, comment="")
    uid = Column(Integer, comment="")
    guid = Column(String(100), comment="")
    complainable = Column(Integer, comment="")
    complained = Column(Integer, comment="")
    replyCount = Column(Integer, comment="回复数")
    md5code = Column(String(55), comment="")
    sentiment = Column(Integer, comment="情感态度")
    shop_name = Column(String(255), comment="店铺名称")

    def __repr__(self):
        return self.name


def init_db():
    engine = create_engine(
        "mysql+pymysql://%s" % config.get('database'),
        max_overflow=0,
        pool_size=5,
        pool_timeout=30,
        pool_recycle=-1,
        # echo=True
    )
    return sessionmaker(bind=engine)()


def init_redis():
    host = config.get("redis").get("host")
    port = config.get("redis").get("port")
    db = config.get("redis").get("db")
    password = AesEncryption(config.get("secret_key")).decryption(config.get("redis").get("password"))
    pool = redis.ConnectionPool(host=host, port=port, db=db, password=password)
    r = redis.Redis(connection_pool=pool)
    return r


db = init_db()
cache = init_redis()

# sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel = '%s' order by insert_time desc limit 1" % (time.strftime(
#                     "%Y-%m-%d", time.localtime()), "jd")
# print(sql)
# cur = db.execute(sql)
# row = cur.fetchone()
# print(row)
# if not row:
#     pass
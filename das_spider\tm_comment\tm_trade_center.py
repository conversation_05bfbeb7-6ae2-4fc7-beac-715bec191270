import datetime
import json
import random
import time
import traceback
import platform
import urllib3
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.support.wait import Web<PERSON>river<PERSON>ait
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.common.keys import Keys
from lxml import etree
import __init__
from dateutil.parser import parse
from retrying import retry

from tm_comment.tm_login import TmClientSpider
from utils.logx import logger
from database import db, rds

def random_sleep(min_v=1, max_v=3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


class TmTradeCenter(TmClientSpider):
    def __init__(self):
        super(TmTradeCenter, self).__init__()

    def order_manage(self):
        # self.account_login()
        submit = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//p[@title="已卖出的宝贝"]'))
        )
        submit.click()
        random_sleep(2, 5)
        all_handles = self.browser.window_handles
        print(all_handles)
        self.browser.switch_to.window(all_handles[-1])
        if self.browser.current_url.startswith("https://aq.taobao.com/durex/middle"):
            return self.verify()

    def verify(self):
        self.browser.switch_to.frame(0)

        for _ in range(2):
            # 获取验证码
            get_code_but = self.wait.until(
                Ec.element_to_be_clickable((By.XPATH, '//input[contains(@class, "send-code-btn-normal")]'))
            )
            # get_code_but.click()
            logger.info('获取验证码')
            # 验证码有效期15分钟
            for _ in range(3):
                time.sleep(60 * 3)
                sql = "select check_code from tianmao_comment_check_code where insert_time > '%s' and channel in ('%s') order by insert_time desc limit 1" % (
                    time.strftime(
                        "%Y-%m-%d", time.localtime()), 'test')
                cur = db.execute(sql)
                row = cur.fetchone()
                if not row:
                    logger.info("数据未查到")
                    continue
                res = row[0]
                send_code_inp = self.wait.until(
                    Ec.element_to_be_clickable((By.XPATH, '//input[contains(@class, "J_SafeCode")]'))
                )
                send_code_inp.send_keys(res)
                logger.info("%s验证码已输入" % res)
                time.sleep(1)

                submit_btn = self.wait.until(
                    Ec.element_to_be_clickable((By.ID, 'J_FooterSubmitBtn'))
                )
                submit_btn.click()
                logger.info("验证码已提交")
                time.sleep(3)

                if self.complete(10):
                    logger.info("验证通过")
                    return True
        logger.error('验证码失败')

    def test1(self):
        url = 'https://www.baidu.com'
        # 1. 请求网址
        self.browser.get(url)
        submit = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'kw'))
        )
        submit.send_keys('abc')
        time.sleep(3)
        return
    def test2(self):
        submit = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'su'))
        )
        submit.click()
        time.sleep(3)

        pass

if __name__ == '__main__':
    # TmClientSpider().account_login()
    trade = TmTradeCenter()
    trade.test1()
    trade.test2()
    # TmTradeCenter().order_manage()
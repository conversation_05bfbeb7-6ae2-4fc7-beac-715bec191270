import datetime
import platform
import traceback

import __init__
from selenium.webdriver.support import expected_conditions as Ec
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from sqlalchemy import func
from tm_comment_client import Tm<PERSON>lient<PERSON>pider, random_sleep, db, logger
from utils.dingtalk import DingTalk
from models import TianmaoComment
from settings import config


class MonitorTmComment(TmClientSpider):
    def __init__(self, start_date, end_date):
        super(MonitorTmComment, self).__init__(start_date, end_date)
        self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
                  secret=config.get("ding_talk").get("secret"))

    def start(self):
        try:
            total_dic = {}
            self.account_login()
            comment_type_dic = {'main': "评论", 'append':"追评"}
            for comment_type in comment_type_dic:
                self.comment_type = comment_type
                total = self.get_comment()
                total_dic[comment_type] = {"page_total": total}
            self.browser.quit()
            start_time = self.start_date + ' 00:00:00'
            end_time = self.end_date + ' 23:59:59'
            main_res = db.query(func.count(TianmaoComment.id)).filter(
                TianmaoComment.first_comment_time.between(start_time, end_time)).scalar()
            total_dic["main"]["database_total"] = main_res
            append_res = db.query(func.count(TianmaoComment.id)).filter(
                TianmaoComment.append_comment_time.between(start_time, end_time)).scalar()
            total_dic["append"]["database_total"] = append_res


            notice = f'{str(datetime.datetime.now()).split()[0]} 天猫评论监控日报：'

            for comment_type, total_data in total_dic.items():
                page_total = total_data.get("page_total")
                database_total = total_data.get("database_total")
                if page_total == database_total:
                    notice += f"\n\t{self.start_date} - {self.end_date} {comment_type_dic.get(comment_type)}数据 无误"
                else:
                    notice += f"\n\t{self.start_date} - {self.end_date} {comment_type_dic.get(comment_type)}数据{page_total}条，入库{database_total}条，相差{page_total - database_total}条"
            self.ding_talk.send_text(text=notice, is_at_all=False, at_mobiles=["15879067265"])
        except:
            self.browser.quit()
            notice = '天猫评论监控程序：异常'
            self.ding_talk.send_text(text=notice, is_at_all=False, at_mobiles=["15879067265"])
            traceback.print_exc()

    def get_comment(self):
        self.select_option()
        # 开始时间
        start_date_inp = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'input[placeholder="开始日期"]'))
        )
        if platform.system() == 'Darwin':
            start_date_inp.send_keys(Keys.COMMAND, 'a')
        else:
            start_date_inp.send_keys(Keys.CONTROL, 'a')
        random_sleep()
        start_date_inp.send_keys(self.start_date)
        random_sleep()

        # 结束时间
        end_date_inp = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, 'input[placeholder="结束日期"]'))
        )
        if platform.system() == 'Darwin':
            end_date_inp.send_keys(Keys.COMMAND, 'a')
        else:
            end_date_inp.send_keys(Keys.CONTROL, 'a')
        end_date_inp.send_keys(self.end_date)
        random_sleep()
        end_date_inp.send_keys(Keys.ENTER)
        random_sleep(3)

        total_data = self.wait.until(
            Ec.element_to_be_clickable((By.XPATH, '//li[@class="ant-pagination-total-text"]'))
        )
        total = int(total_data.text.split()[1])
        return total


if __name__ == '__main__':
    now = datetime.datetime.now()
    end = str(now - datetime.timedelta(days=1)).split()[0]
    start = str(now - datetime.timedelta(days=7)).split()[0]
    monitor = MonitorTmComment(start, end)
    monitor.start()

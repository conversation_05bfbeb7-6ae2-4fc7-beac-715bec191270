import os
import sys
import json
import nacos

REMOTE_CONFIG = {
    # "test": {
    #     "server_address": "**************:8848",
    #     "namespace": "spider",
    #     "data_id": "boss",
    #     "group": "group",
    # },
    # "pro": {
    #     "server_address": "**************:30632",
    #     "namespace": "spider",
    #     "data_id": "boss",
    #     "group": "group",
    # }
}


def init_config():
    argv = sys.argv
    mode = str
    if len(argv) > 1:
        mode = argv[1]
    else:
        mode = ""

    conf = dict
    if mode == 'pro':
        conf = REMOTE_CONFIG.get("pro")
    elif mode == 'test':
        conf = REMOTE_CONFIG.get("test")
    else:
        return json.load(open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dev.json'), encoding='utf8'))
    # no auth mode
    client = nacos.NacosClient(conf.get("server_address"), namespace=conf.get("namespace"))
    # auth mode
    # client = nacos.NacosClient(SERVER_ADDRESSES, namespace=NAMESPACE, ak="{ak}", sk="{sk}")
    return json.loads(client.get_config(conf.get("data_id"), conf.get("group")))


_config = init_config()


def get(key):
    return _config.get(key)
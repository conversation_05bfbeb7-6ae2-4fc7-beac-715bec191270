import json
import datetime
import time
import requests
import __init__
from database import rds, db
from tm_comment.tm_login import Tm<PERSON><PERSON><PERSON><PERSON><PERSON>, random_sleep
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as Ec
from utils.logx import logger


class JkClientSpider(TmClientSpider):
    def __init__(self):
        super(Jk<PERSON><PERSON><PERSON>pider, self).__init__()
        self.jk_cookie = self.get_jk_cookie()

    def run(self, start_date, end_date):
        start_time = int(round(time.mktime(time.strptime(start_date, "%Y-%m-%d")) * 1000))
        end_time = int(round(time.mktime(time.strptime(end_date, "%Y-%m-%d")) * 1000))
        while end_time >= start_time:
            tmp_start_time = end_time - 86400000
            page = 1
            while self.get_jk_message_data(tmp_start_time, end_time, page):
                page += 1
                time.sleep(3)

            end_time = tmp_start_time

    def get_jk_message_data(self, start_time, end_time, page):
        url = 'https://crm.jkcrm.cn/cloud/ordercenter/urge-payment-record/list-record'
        data = {
            "endTime": end_time,
            "page": page,
            "pageSize": 50,
            "startTime": start_time,
            "phoneNum": "",
            "recordType": 0,
            "taskId": "0",
            "taskName": "",
            "taskType": "-1",
            "tid": "",
            "timeScope": 1
        }
        logger.info("get_jk_message_data req", start_time, end_time, page)
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 Safari/537.36",
        }
        for i in range(3):
            req = requests.request(method='POST', url=url, json=data, headers=headers, cookies=self.jk_cookie)
            data = req.json()
            logger.info("get_jk_message_data rsp", data)
            if data.get("code") != 200:
                if i < 2:
                    logger.info(f"登录失效 重试第{i+1}次")
                    self.account_login()
                    self.jk_cookie = self.get_jk_cookie()
                else:
                    logger.error("超出重试次数")
                    return
            else:
                break
        if page == 1 or page == 2:
            send_date = datetime.datetime.fromtimestamp(start_time / 1000)
            count =  data.get("result").get("sumCountAll")
            cur = self.get_message_record(send_date)
            res = cur.fetchone()
            if res:
                self.update_message_record(send_date, count)
            else:
                self.create_message_record(send_date, count)
            db.commit()
        if not data.get("result").get("records"):
            return False
        sql = """
            INSERT IGNORE INTO jk_message_record_detail (created_at, updated_at, message_id, content, ouid, send_time, service_name, task_id, task_name, task_type, 
            task_type_str, tid, word_num, word_page, wx_nick, phone_num, send_state, ttime) 
            VALUES
        """
        now = datetime.datetime.now()
        for v in data.get("result").get("records"):
            send_time = v.get("sendTime")
            if send_time:
                send_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(v.get("sendTime") / 1000))
            ttime = v.get("ttime")
            if ttime:
                ttime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(v.get("ttime") / 1000))
            sql += "('%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, '%s', %d, '%s', '%s', %d, %d, '%s', '%s', '%s', '%s')," % (
                now, now, v.get("id"), v.get("content"), v.get("ouid"),
                send_time,
                v.get("serviceName"),
                v.get("taskId"),
                v.get("taskName"), v.get("taskType"), v.get("taskTypeStr"), v.get("tid"), v.get("wordNum"),
                v.get("wordPage"),
                v.get("wxNick"), v.get("phoneNum"), v.get("sendState"),
                ttime
            )
        sql = sql.strip(", ").replace("'None'", "null")
        db.execute(sql)
        db.commit()
        return True

    def get_message_record(self, date):
        sql = '''
            select * from message_record where send_date = '%s'
        ''' % date
        return db.execute(sql)

    def update_message_record(self, date, count):
        sql = '''
            update message_record set send_number = '%s' where send_date = '%s'
        ''' % (count, date)
        db.execute(sql)

    def create_message_record(self, date, count):
        sql = '''
            insert into message_record(`send_date`, `send_number`) values('%s', '%s')
        ''' % (date, count)
        db.execute(sql)


if __name__ == '__main__':
    JkClientSpider().run("2022-05-31", "2022-06-30")
import pymongo


class MongoDB:
    def find(self, param):
        return self.collection.find(param)

    def find_one(self, param):
        return self.collection.find_one(param)

    def insert_one(self, data):
        return self.collection.insert_one(data)

    def update_one(self, param, data):
        return self.collection.update_one(param, {"$set": data})


if __name__ == '__main__':
    mongo = MongoDB(db="data_access", collection="jd_comment")
    res = mongo.update_one({"id": '123'}, {"id": "234"})
    if res.matched_count:
        print(res)
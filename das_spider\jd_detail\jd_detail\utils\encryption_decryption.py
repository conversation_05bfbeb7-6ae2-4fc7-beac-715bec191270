# -*- coding: utf-8 -*-
import base64
from Crypto.Cipher import AES
from Crypto import Random


class EncryptionDecryption:
    def __init__(self, secret_key):
        self.secret_key = secret_key.encode("utf-8")
        self.iv = Random.new().read(16)

    def aes_encryption(self, text=None):
        """ AES加密 ,通过aes加密+iv向量+base64编码 """

        # 1、创建一个aes对象
        text = text.encode("utf-8")
        aes = AES.new(self.secret_key, AES.MODE_CFB, self.secret_key)
        # 2、附加上iv值是为了在解密时找到在加密时用到的随机iv
        encrypt_msg = self.iv + aes.encrypt(text)
        # 3、通过base64编码重新进行一次编码
        result = str(base64.b64encode(encrypt_msg), encoding='utf-8')
        return result

    def aes_decryption(self, ciphertext=None):
        """AES解密，ciphertext：密文"""

        # 1、创建一个aes对象
        aes = AES.new(self.secret_key, AES.MODE_CFB, self.secret_key)
        # 2、将密文先进行base64反编译
        decrypt_msg = base64.b64decode(ciphertext)
        # 3、后十六位是真正的密文,进行解密
        result = aes.decrypt(decrypt_msg[16:]).decode('utf-8')
        return result

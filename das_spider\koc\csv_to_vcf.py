import json
import os
import pandas as pd


def csv2vcf(file):
    rf = open(file).read().split("\n")
    name = file_path_shortname_extension(file)
    print(name)
    print(name[0]+name[1]+".vcf")
    vcf_path = os.path.join(name[0], name[1]+".vcf")
    with open(vcf_path, "w", encoding="utf-8") as wf:
        count = 0
        for line in rf:
            content = ["BEGIN:VCARD", "VERSION:2.1", "", "", "END:VCARD\n\n", ]
            title = line.split(",")
            if title[0] == "" or not title[0].strip('"').isdigit():
                continue
            # if not title[0] == "":
            #     content[2] = "N:" + "宝宝馋了02"
            if not title[1] == "":
                content[2] = "FN:" + "宝宝馋了02-%s" % title[1].strip('"')
            if not title[0] == "":
                content[3] = "TEL;CELL;VOICE:" + title[0].strip('"')
            str="\n".join(content)
            wf.write(str)
            count += 1
            # if count > 200:
            #     break
    print(f"{count}条数据写入完成")



def csv2vcf(file):
    rf = open(file).read().split("\n")
    name = file_path_shortname_extension(file)
    print(name)
    print(name[0]+name[1]+".vcf")
    vcf_path = os.path.join(name[0], name[1]+".vcf")
    with open(vcf_path, "w", encoding="utf-8") as wf:
        count = 0
        for line in rf:
            content = ["BEGIN:VCARD", "VERSION:2.1", "", "", "END:VCARD\n\n", ]
            title = line.split(",")
            if title[0] == "" or not title[0].strip('"').isdigit():
                continue
            # if not title[0] == "":
            #     content[2] = "N:" + "宝宝馋了02"
            content[2] = "FN:" + "宝宝馋了02-%s" % title[0].strip('"')
            content[3] = "TEL;CELL;VOICE:" + title[0].strip('"')
            str="\n".join(content)
            wf.write(str)
            count += 1
            # if count > 200:
            #     break
    print(f"{count}条数据写入完成")


def file_path_shortname_extension(file):
    #返回文件拓展名.csv 等
    (filepath,temp_filename) = os.path.split(file)
    (shortname,extension) = os.path.splitext(temp_filename)
    return (filepath,shortname,extension)


def run(file):
    if not os.path.isfile(file) :
        print("文件不存在")
    else:
        a = file_path_shortname_extension(file)[2]
        if a == ".csv" :
            print("转换开始")
            csv2vcf(file)
            print("已生成vcf文件")
        else :
            print("请选择正确的csv文件")


if __name__ == '__main__':
    # run('/Users/<USER>/Downloads/koc-test-15-02.csv')
    run('../ttt.csv')

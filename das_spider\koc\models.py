from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, UniqueConstraint, Index, event


Base = declarative_base()


class KocInfo(Base):
    __tablename__ = "20220317kocmobile"
    # 自己添加字段
    id = Column(Integer(), primary_key=True)
    receiver_mobile = Column(String(11), index=True, nullable=False, comment="收货人手机号")
    xhs_is_register = Column(Integer(), comment="小红书渠道：是否注册")
    xhs_note_count = Column(Integer(), comment="小红书渠道：笔记数量")
    xhs_nickname = Column(String(55), comment="小红书渠道：用户昵称")
    xhs_images = Column(String(255), comment="小红书渠道：用户头像")
    xhs_userid = Column(String(55), comment="小红书渠道：用户userid")
    xhs_redid = Column(String(55), comment="小红书渠道：用户redid")

    def __repr__(self):
        return self.receiver_mobile


import inspect
import os
import logging.config
import platform

# 定义三种日志输出格式 开始
import time

standard_format = '[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s]' \
                  '[%(levelname)s] %(message)s'
simple_format = '[%(levelname)s][%(asctime)s] %(message)s'
id_simple_format = '[%(levelname)s][%(asctime)s] %(message)s'
# 定义日志输出格式 结束

logfile_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # log文件的目录，需要自定义文件路径
logfile_dir = os.path.join(logfile_dir, 'log')
dir_time = time.strftime('%Y-%m-%d', time.localtime())

# 如果不存在定义的日志目录就创建一个
if not os.path.isdir(logfile_dir):
    os.mkdir(logfile_dir)

# log文件的全路径
logfile_path_dic = {
    logging.INFO: os.path.join(logfile_dir, "info_%s.log" % dir_time),
    logging.WARNING: os.path.join(logfile_dir, "warning_%s.log" % dir_time),
    logging.ERROR: os.path.join(logfile_dir, "error_%s.log" % dir_time),
}


# log配置字典
LOGGING_DIC = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': standard_format
        },
        'simple': {
            'format': simple_format
        },
    },
    'filters': {},
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        # 打印到文件的日志,收集info及以上的日志
        'info': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.INFO],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        },
        'warning': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.WARNING],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'standard',
            'filename': logfile_path_dic[logging.ERROR],
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'encoding': 'utf-8',
        }
    },
    'loggers': {
        'DEBUG': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'INFO': {
            'handlers': ['info', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'WARNING': {
            'handlers': ['warning', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'ERROR': {
            'handlers': ['error', 'console'],
            'level': 'ERROR',
            'propagate': False,
        }
    },
}


class Logx:
    def __init__(self):
        self.__logger = {}
        logging.config.dictConfig(LOGGING_DIC)
        for k, v in LOGGING_DIC.get("loggers").items():
            self.__logger[k] = logging.getLogger(k)

    def getLogMessage(self, *args):
        '''
            自定义增加输出：来源文件名以及行号，格式为[代码记录] 信息
        '''
        frame, filename, line_no, function_name, code, unknow_field = inspect.stack()[2]
        cur_system = platform.system()
        if cur_system == "Windows":
            filename = filename.split("\\")[-1]
        else:
            filename = filename.split("/")[-1]
        msg = "[%s:%s:%s] " % (filename, line_no, function_name)
        for i in range(len(args)):
            msg += str(args[i])
            if i != len(args) - 1:
                msg += ' '
        return msg

    def debug(self, *args):
        self.__logger["DEBUG"].debug(self.getLogMessage(*args))

    def info(self, *args):
        self.__logger["INFO"].info(self.getLogMessage(*args))

    def warning(self, *args):
        self.__logger["WARNING"].warning(self.getLogMessage(*args))

    def error(self, *args):
        self.__logger["ERROR"].error(self.getLogMessage(*args))


def load_my_logging_cfg():
    logger = Logx()
    logger.info('It works!')
    return logger


logger = load_my_logging_cfg()

if __name__ == '__main__':
    logger.info("123", 13, 213, (2, 3), {1, 3})
    # logger.warning("123")
    # logger.error("123")

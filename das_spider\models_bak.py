# coding: utf-8
import datetime
import time
from builtins import list

from pymysql import MySQLError
from sqlalchemy import Column, DateTime, String, text, DECIMAL, Date, event, Table, MetaData, Text, or_, Index, orm, \
	Integer, and_, TEXT

from sqlalchemy.dialects.mysql import INTEGER, VARCHAR
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.orm import Session
from utils.logx import logger
from sqlalchemy import text
from sqlalchemy import column
from sqlalchemy.sql import func
from threading import Lock

_Base = declarative_base()
metadata = _Base.metadata


class BaseModel(_Base):
	__abstract__ = True

	id = Column(INTEGER(11), primary_key=True)
	created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
	updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
	deleted_at = Column(DateTime, comment='删除时间')
	remark = Column(VARCHAR(55), comment='备注')

	@staticmethod
	def get_dict_data(data: list):
		return [dict(zip(item.keys(), item)) for item in data]

	@classmethod
	def insert_by_dict(cls, db, data):
		item = cls(**data)
		db.add(item)
		# db.commit()
		db.flush()
		return item

	@classmethod
	def get(cls, db, id):
		return db.query(cls).filter(cls.id == id).first()

	@classmethod
	def insert(cls, db, item):
		db.add(item)
		# db.commit()
		db.flush()
		return item

	@classmethod
	def delete(cls, db, id):
		return db.query(cls).filter(cls.id == id).update({"deleted_at": datetime.datetime.now()})

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):
		"""
		批量插入数据，有则更改，无则新增（需有一个唯一键）
		params data: 数据
		params update_keys: 数据存在时，执行修改操作的字段
		"""
		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)

	@classmethod
	def merge(cls, src: dict):
		"""
        :param src:
        :return:
        """
		attributes = [attr for attr in cls.__dict__ if not callable(getattr(cls, attr)) and not attr.startswith("_")]
		instance = cls()
		for attr in attributes:
			if isinstance(src, dict):
				if src.get(attr) is not None:
					setattr(instance, attr, src[attr])
		# todo 其他类型晚点再写
		return instance


@event.listens_for(BaseModel, 'before_insert')
def receive_before_create(mapper, connection, target):
	"listen for the 'before_insert' event"
	target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
	target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(BaseModel, 'before_update')
def receive_before_update(mapper, connection, target):
	"listen for the 'before_update' event"
	target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(Session, "do_orm_execute")
def _add_filtering_criteria(execute_state):
	"""Intercept all ORM queries.   Add a with_loader_criteria option to all
	of them.

	This option applies to SELECT queries and adds a global WHERE criteria
	(or as appropriate ON CLAUSE criteria for join targets)
	to all objects of a certain class or superclass.

	"""

	# the with_loader_criteria automatically applies itself to
	# relationship loads as well including lazy loads.   So if this is
	# a relationship load, assume the option was set up from the top level
	# query.

	if (
			not execute_state.is_column_load and not execute_state.is_relationship_load and not execute_state.execution_options.get(
		"include_private", False)):
		execute_state.statement = execute_state.statement.options(orm.with_loader_criteria(
			BaseModel,
			lambda cls: cls.deleted_at == None,
			include_aliases=True,
		))


class BrandChannelInfo(BaseModel):
	__tablename__ = 'brand_channel_info'
	__table_args__ = {'comment': '品牌渠道信息表\\n'}

	channel = Column(String(25), nullable=False, comment='渠道，如TM、JD、DY')
	channel_str = Column(String(25), nullable=False, comment='渠道释义，如天猫、京东、抖音')
	platform = Column(String(55), nullable=False, comment='平台，如chanmama、sycm、moojing')
	platform_str = Column(String(55), nullable=False, comment='平台释义，如馋妈妈、生意参谋、魔镜')
	brand_name = Column(String(55), nullable=False, comment='品牌名称')
	brand_id = Column(String(55), nullable=False, comment='品牌id')
	brand_basic_info_id = Column(INTEGER(11), nullable=False, comment='brand_basic_info表主键')
	required_type = Column(String(255), comment='需要的类目以逗号分割')

	def __repr__(self):
		return self.brand_name.__str__()

	@classmethod
	def list(cls, db, channel, platform):
		lis = db.query(cls).filter(cls.channel == channel, cls.platform == platform).all()
		return lis

	@classmethod
	def get_by_brand_id(cls, db, brand_id, channel, platform):
		item = db.query(cls).filter(cls.brand_id == brand_id, cls.channel == channel, cls.platform == platform).first()
		return item

	@classmethod
	def insert(cls, db, data):
		item = cls(**data)
		db.add(item)
		# db.commit()
		db.flush()
		return item

	@staticmethod
	def init_to_dict(data):
		dic = {}
		for i in data:
			dic[i.brand_id] = {"brand_name": i.brand_name, 'required_type': i.required_type}
		return dic

	@staticmethod
	def to_dict(data):
		dic = {}
		for i in data:
			dic[i.brand_id] = i
		return dic
	
	@staticmethod
	def to_dict_name_id(data):
		dic = {}
		for i in data:
			dic[i.brand_name] = i
		return dic


class BrandDyMonthTrade(BaseModel):
	__tablename__ = 'brand_dy_month_trade'
	__table_args__ = {'comment': '抖音各品牌月度交易数据表\\n'}

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(String(55), nullable=False, comment='品牌id')
	month = Column(DateTime, nullable=False, comment='月份')
	trade_money = Column(DECIMAL(20, 3), nullable=False, comment='销售额')
	brand_self_broadcast = Column(DECIMAL(20, 3), nullable=False, comment='品牌自播销售额')
	influencer_broadcast = Column(DECIMAL(20, 3), nullable=False, comment='达人播销售额')
	store_broadcast = Column(DECIMAL(20, 3), nullable=False, comment='店播销售额')
	brand_channel_info_id = Column(INTEGER(11), nullable=False, comment='brand_channel_info表id')

	def __repr__(self):
		return self.brand_name.__str__()

	@classmethod
	def list(cls, db, month):
		lis = db.query(cls).filter(cls.month == month).all()
		return lis

	@classmethod
	def get(cls, db, brand_id, month):
		item = db.query(cls).filter(cls.brand_id == brand_id, cls.month == month).first()
		return item

	@classmethod
	def insert(cls, db, item):
		db.add(item)
		db.flush()
		return item


class BrandTmTrade(BaseModel):
	__abstract__ = True
	__table_args__ = (
		Index('udx_brand_tm_month_trade_brand_id_date', 'brand_id', 'start_date', 'end_date', unique=True),
		{'comment': '天猫各品牌交易数据基础表\\n'})

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(String(55), nullable=False, comment='品牌id')
	trade_money = Column(DECIMAL(20, 3), nullable=False, comment='销售额')
	brand_channel_info_id = Column(INTEGER(11), nullable=False, comment='brand_channel_info表id')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	brand_35 = Column(DECIMAL(20, 3), nullable=False, comment='奶粉/辅食/营养品/零食', default=0)
	brand_50002766 = Column(DECIMAL(20, 3), nullable=False, comment='零食/坚果/特产', default=0)
	brand_50016422 = Column(DECIMAL(20, 3), nullable=False, comment='粮油调味/速食/干货/烘焙', default=0)
	brand_50050359 = Column(DECIMAL(20, 3), nullable=False, comment='水产肉类/新鲜蔬果/熟食', default=0)

	def __repr__(self):
		return self.brand_name.__str__()

	@classmethod
	def insert_by_dict(cls, db, data):
		item = cls(**data)
		db.add(item)
		db.flush()
		return item

	@classmethod
	def get(cls, db, brand_id, start_date, end_date):
		item = db.query(cls).filter(cls.brand_id == brand_id, cls.start_date == start_date,
									cls.end_date == end_date).first()
		return item
	
	@classmethod
	def get_info(cls,db,brand_name, start_date, end_date):
		item = db.query(cls).filter(cls.brand_name == brand_name, cls.start_date == start_date,
									cls.end_date == end_date).first()
		return item


class BrandTmMonthTrade(BrandTmTrade):
	__tablename__ = 'brand_tm_month_trade'
	__table_args__ = {'comment': '天猫各品牌月度交易数据\\n'}

	month = Column(DateTime, nullable=False, comment='月份')


class BrandTmWeekTrade(BrandTmTrade):
	__tablename__ = 'brand_tm_week_trade'
	__table_args__ = {'comment': '天猫各品牌周交易数据\\n'}


class BrandTmDayTrade(BrandTmTrade):
	__tablename__ = 'brand_tm_day_trade'
	__table_args__ = {'comment': '天猫各品牌天交易数据\\n'}


class BrandMonthSpuTradeMatchInfo(BaseModel):
	__tablename__ = 'brand_month_spu_trade_match_info'
	__table_args__ = (
		Index('ind_brand_month_spu_trade_match_info_channel_platform', 'channel', 'platform'),
		Index('udx_brand_tm_month_goods_trade_info_goods_id_brand_id_date', 'goods_id', 'start_date', 'end_date',
			  'brand_id', unique=True),
		{'comment': '竞对品牌 月spu销售匹配数据表'}
	)

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	category_name = Column(VARCHAR(25), comment='商品类目')
	series_name = Column(VARCHAR(55), comment='商品系列')
	sub_category_name = Column(VARCHAR(25), comment='商品子类目')
	spu_name = Column(VARCHAR(55), index=True, comment='商品SPU')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	channel = Column(VARCHAR(25), nullable=False, comment='渠道，如TM、JD、DY')
	platform = Column(VARCHAR(55), nullable=False, comment='平台，如chanmama、sycm、moojing')
	brand_channel_info_id = Column(Integer, comment='brand_channel_info表id')
	status = Column(Integer, comment='状态: 0 系统匹配 1人工匹配')
	match_status = Column(Integer,
						  comment='匹配状态，0. 初始值;1. 根据旧有id匹配成功;2. 根据旧有id匹配成功，但name发生改变，根据spu匹配失败;3. 根据旧有id匹配成功，但name发生改变，根据spu匹配成功;4. 根据旧有id匹配失败，根据spu匹配失败;5. 根据旧有id匹配失败，根据spu匹配成功,10. 人工匹配')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	time_mode = Column(VARCHAR(255), comment='day, week, month')
	is_new = Column(Integer, comment='是否是新出现的id')
	last_collection_time = Column(DateTime, comment='最后一次采集时间')
	trade_money = Column(Integer, comment='交易金额')

	@classmethod
	def list(cls, db, start_date, end_date, channel, platform, goods_ids, page_no: int = 1, page_size: int = 99999):
		db = db.query(cls.id, cls.spu_name, cls.goods_name, cls.goods_id, cls.trade_money) \
			.filter(cls.channel == channel, cls.platform == platform)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		if goods_ids:
			db = db.filter(cls.goods_id.in_(goods_ids))
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		return db.query(cls).filter(cls.id == id).update(data)

	@classmethod
	def insert_by_trade_info(cls, db, match_info):
		# channel_info_item = BrandChannelInfo.get_by_brand_id(db, match_info.get('brand_id'))
		# if not channel_info_item:
		#     # channel_info_item = BrandChannelInfo.insert(db, {
		#     #     "channel": "TM",
		#     #     "channel_str": "天猫",
		#     #     "platform": "sycm",
		#     #     "platform_str": "生意参谋",
		#     #     "brand_name": trade_info_item.brand_name,
		#     #     "brand_id": trade_info_item.brand_id
		#     # })
		#     # TODO 需要人工增加brand_basic_info_id
		#     exit("发现新增的品牌id：{0}，请人工匹配品牌".format(trade_info_item.brand_id))
		# goods_data["brand_name"] = trade_info_item.brand_name
		# goods_data["brand_id"] = trade_info_item.brand_id
		# goods_data["goods_id"] = trade_info_item.goods_id
		# goods_data["goods_url"] = trade_info_item.goods_url
		# goods_data["brand_channel_info_id"] = channel_info_item.id
		goods_info_item = cls(**match_info)
		db.add(goods_info_item)
		# db.flush()
		# db.commit()
		return goods_info_item

	# @classmethod
	# def spider_list(cls, db, start_date, end_date, spu_name_list):
	# 	lis = db.query(cls).filter(cls.start_date >= start_date,
	# 							   cls.end_date <= end_date, cls.spu_name.in_(spu_name_list),
	# 							   cls.channel == "TM", cls.platform == "sycm",
	# 							   cls.time_mode == "month"
	# 							   ).all()
	# 	return lis

	@classmethod
	def spider_list(cls, db, spu_name_list):
		lis = db.query(cls).filter(cls.spu_name.in_(spu_name_list),
								   # cls.channel == "TM", cls.platform == "sycm",
								   # cls.time_mode == "month"
								   ).all()
		return lis

	@classmethod
	def get_spu_by_latest(cls, db, channel, platform, mode):
		mode_sql = ""
		if channel == "TM" and platform == "sycm":
			# 由于2023-10-01起，周数据开始人工匹配，所以该日期后的周数据，也参与系统匹配过程
			week_data_match_time = datetime.datetime.strptime("2023-10-01", "%Y-%m-%d")
			mode_sql = "and (start_date >= '%s' or time_mode = 'month')" % week_data_match_time
		sql = """
            select brand_month_spu_trade_match_info.goods_id, goods_name, spu_name, match_status from brand_month_spu_trade_match_info 
            JOIN (
            select goods_id, max(`end_date`) max_end_date from brand_month_spu_trade_match_info
            where deleted_at is null
            and channel = '%s'
            and platform = '%s'
            %s
            GROUP BY goods_id
            ) t on brand_month_spu_trade_match_info.goods_id = t.goods_id and brand_month_spu_trade_match_info.end_date  = t.max_end_date
            where deleted_at is null
        """ % (channel, platform, mode_sql)
		res = db.execute(sql)
		return cls.get_dict_data(res)

	@classmethod
	def get_spu_by_new_data(cls, db, channel, platform,goods_name,goods_id=None):
		db = db.query(cls).filter(
			and_(cls.channel == channel,cls.platform == platform, cls.goods_name == goods_name))
		if channel == "TM" and platform == "sycm":
			db = db.filter(
				and_((or_(cls.start_date >= '2023-10-01',cls.time_mode == 'month')))
				)
		if goods_id:
			db=db.filter(cls.goods_id == goods_id)
		return db.order_by(cls.end_date.desc()).first()

	@classmethod
	def delete_expired_data(cls, db, match_info):
		goods_id = match_info.get("goods_id")
		start_date = match_info.get("start_date")
		db.query(cls).filter(cls.start_date == start_date, cls.goods_id == goods_id).update(
			{"deleted_at": datetime.datetime.now()})
		pass

	@classmethod
	def list_by_spu_list(cls, db, start_date: str, end_date: str, spu_list: list, channel: str, platform: str,
						 page_no=1, page_size=99999) -> list:
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.start_date, cls.end_date,
					  cls.trade_money).filter(cls.channel == channel, cls.platform == platform,
											  cls.start_date == start_date, cls.spu_name.in_(spu_list))
		if end_date:
			db = db.filter(cls.end_date == end_date)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)


class BrandTmGoodsTradeInfo(BaseModel):
	__abstract__ = True
	__table_args__ = ({'comment': '天猫各品牌商品交易数据基础表\\n'})

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	category_name = Column(VARCHAR(25), comment='商品类目')
	series_name = Column(VARCHAR(55), comment='商品系列')
	sub_category_name = Column(String(25), comment='商品子类目')
	spu_name = Column(VARCHAR(55), comment='商品SPU')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	trade_money = Column(INTEGER(11), comment='交易金额')
	visitors_nums = Column(INTEGER(11), comment='访客人数')
	growth_rate = Column(DECIMAL(10, 5), comment='增长幅度')
	search_nums = Column(INTEGER(11), comment='搜索人数')
	search_rate = Column(DECIMAL(10, 5), comment='搜索占比')
	buyer_nums = Column(INTEGER(11), comment='买家数')
	payment_conversion_rate = Column(DECIMAL(10, 5), comment='支付转化率')
	atv = Column(DECIMAL(20, 3), comment='客单价')
	uv_value = Column(DECIMAL(20, 3), comment='UV价值')
	match_status = Column(INTEGER(11),
						  comment='匹配状态，0. 初始值;1. 根据旧有id匹配成功;2. 根据旧有id匹配成功，但name发生改变，根据spu匹配失败;3. 根据旧有id匹配成功，但name发生改变，根据spu匹配成功;4. 根据旧有id匹配失败，根据spu匹配失败;5. 根据旧有id匹配失败，根据spu匹配成功')
	brand_tm_month_goods_info_id = Column(INTEGER(11), comment='brand_tm_month_goods_info表id')
	brand_tm_category_id = Column(INTEGER(11), comment='商品类目id')
	payment_money_rank = Column(INTEGER(11), comment='支付金额排名')

	# @classmethod
	# def list(cls, db, month: str, page_no, page_size: int) -> list:
	#     db = db.query(cls.id, cls.brand_name,
	#                    cls.brand_id, cls.goods_name,
	#                    cls.goods_id)\
	#         .filter(cls.spu_name == None)
	#     if month:
	#         db = db.filter(cls.month == month)
	#     res = db.limit(page_size).all()
	#     return cls.get_dict_data(res)
	@classmethod
	def get_unmatch_goods(cls, db, params: dict) -> list:
		start_date = params.get("start_date")
		end_date = params.get("end_date")
		page_size = params.get("page_size")
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.start_date, cls.end_date, cls.trade_money) \
			.filter(BrandMonthSpuTradeMatchInfo.id == None) \
			.join(BrandMonthSpuTradeMatchInfo,
				  and_(BrandMonthSpuTradeMatchInfo.goods_id == cls.goods_id,
					   BrandMonthSpuTradeMatchInfo.start_date == cls.start_date,
					   BrandMonthSpuTradeMatchInfo.end_date == cls.end_date,
					   BrandMonthSpuTradeMatchInfo.channel == "TM",
					   BrandMonthSpuTradeMatchInfo.platform == "sycm",
					   BrandMonthSpuTradeMatchInfo.deleted_at == None
					   ),
				  isouter=True)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		res = db.order_by(cls.start_date.asc(), cls.id.asc()).limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		db.query(cls).filter(cls.id == id).update(data)

	# db.commit()

	@classmethod
	def get(cls, db, id):
		return db.query(cls).filter(cls.id == id).first()

	@classmethod
	def insert_brand_id(cls, db):
		sql = """
        update %s_tmp t
        join brand_channel_info on t.brand_name = brand_channel_info.brand_name
        set t.brand_id = brand_channel_info.brand_id
        where brand_channel_info.channel = 'TM'
        and brand_channel_info.deleted_at is null;
        """ % cls.__tablename__
		db.execute(sql)

	@classmethod
	def insert_if_null(cls, db):
		sql = """
        INSERT INTO %s SELECT
        t.* 
        FROM
            %s trade
            RIGHT JOIN %s_tmp t ON trade.goods_id = t.goods_id 
            AND trade.start_date = t.start_date
            AND trade.end_date = t.end_date
        WHERE
            trade.id IS NULL
            AND t.brand_id != "";
        """ % (cls.__tablename__, cls.__tablename__, cls.__tablename__)
		db.execute(sql)

	@classmethod
	def update_if_exists(cls, db):
		pass

	@classmethod
	def truncate_table(cls, db):
		sql = """
        TRUNCATE TABLE %s_tmp;
        """ % cls.__tablename__
		db.execute(sql)

	@classmethod
	def list_by_items_info_list(cls, db, start_date: str, end_date: str, category_id: int) -> list:
		res = db.query(cls.id, cls.goods_id, cls.trade_money, cls.payment_money_rank, cls.growth_rate, cls.brand_id).filter(
			cls.start_date == start_date, cls.end_date == end_date, cls.brand_tm_category_id == category_id,).all()
		return cls.get_dict_data(res)
	
	

class BrandTmMonthGoodsTradeInfo(BrandTmGoodsTradeInfo):
	__tablename__ = 'brand_tm_month_goods_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_goods_trade_info_goods_id_brand_id_date', 'goods_id', 'start_date', 'end_date',
			  'brand_id', unique=True),)

	month = Column(DateTime, comment='月份')

	@classmethod
	def list_by_spu_list(cls, db, start_date: str, end_date: str, spu_list: list, page_no, page_size: int) -> list:
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.start_date, cls.end_date,
					  cls.trade_money).filter(cls.start_date == start_date, cls.end_date == end_date,
											  cls.spu_name.in_(spu_list))
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)


class BrandTmWeekGoodsTradeInfo(BrandTmGoodsTradeInfo):
	__tablename__ = 'brand_tm_week_goods_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_goods_trade_info_goods_id_brand_id_date', 'goods_id', 'start_date', 'end_date',
			  'brand_id', unique=True),)


class BrandTmDayGoodsTradeInfo(BrandTmGoodsTradeInfo):
	__tablename__ = 'brand_tm_day_goods_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_goods_trade_info_goods_id_brand_id_date', 'goods_id', 'start_date', 'end_date',
			  'brand_id', unique=True),)


t_dim_itm_sku_list_once = Table('dim_itm_sku_list_once', metadata, Column('category_name', Text, comment='类目名称'),
								Column('series_name', Text, comment='系列名称'), Column('spu_name', Text, comment='spu名称'),
								Column('sku_name', Text, comment='sku名称'), Column('sku_code', Text, comment='商品编码'),
								Column('remark', Text, comment='备注'), Column('status', Text, comment='状态'),
								Column('price_strategy', Text, comment='价格策略'), comment='新版本商品sku目录清单')


def get_all_spu_category(db):
	res = db.query(t_dim_itm_sku_list_once.columns.category_name, t_dim_itm_sku_list_once.columns.series_name,
				   t_dim_itm_sku_list_once.columns.spu_name) \
		.filter(t_dim_itm_sku_list_once.columns.spu_name != None) \
		.group_by(t_dim_itm_sku_list_once.columns.category_name, t_dim_itm_sku_list_once.columns.series_name,
				  t_dim_itm_sku_list_once.columns.spu_name).all()
	return BaseModel.get_dict_data(res)


class SpuList(BaseModel):
	__tablename__ = 'spu_list'
	__table_args__ = {'comment': 'Spu资料表'}

	category_name = Column(VARCHAR(25), comment='商品类目')
	series_name = Column(VARCHAR(55), comment='商品系列')
	sub_category_name = Column(VARCHAR(25), comment='商品子类目')
	spu_name = Column(VARCHAR(55), unique=True, comment='商品SPU')
	is_monitoring = Column(Integer, comment='是否监控')

	@classmethod
	def list(cls, db, is_monitoring, page_no=1, page_size=99999) -> list:
		db = db.query(cls.id, cls.spu_name,
					  cls.sub_category_name, cls.series_name,
					  cls.category_name, cls.is_monitoring)
		if is_monitoring is not None:
			db = db.filter(cls.is_monitoring == is_monitoring)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)


class BrandJdMonthGoodsTradeInfo(BaseModel):
	__tablename__ = 'brand_jd_month_goods_trade_info'
	__table_args__ = (
		Index('udx_webcrawler_brand_tm_month_goods_trade_info_goods_id_month', 'goods_id', 'month', unique=True),)

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	month = Column(DateTime, comment='月份')
	category_name = Column(VARCHAR(25), comment='商品类目')
	series_name = Column(VARCHAR(55), comment='商品系列')
	sub_category_name = Column(VARCHAR(25), comment='商品子类目')
	spu_name = Column(VARCHAR(55), comment='商品SPU')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	pic_url = Column(VARCHAR(255), comment='图片链接')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	trade_money = Column(Integer, comment='交易金额')
	trade_growth_rate = Column(DECIMAL(10, 5), comment='销售额同比')
	volume = Column(Integer, comment='销量')
	volume_growth_rate = Column(DECIMAL(10, 5), comment='销量同比')
	price = Column(DECIMAL(10, 5), comment='价格')
	lowest_price = Column(DECIMAL(10, 5), comment='最低折扣价')
	original_price = Column(DECIMAL(10, 5), comment='原价')
	first_category = Column(VARCHAR(25), comment='一级类目')
	second_category = Column(VARCHAR(25), comment='二级类目')
	third_category = Column(VARCHAR(25), comment='三级类目')
	fourth_category = Column(VARCHAR(25), comment='四级类目')
	match_status = Column(Integer,
						  comment='匹配状态，0. 初始值;1. 根据旧有id匹配成功;2. 根据旧有id匹配成功，但name发生改变，根据spu匹配失败;3. 根据旧有id匹配成功，但name发生改变，根据spu匹配成功;4. 根据旧有id匹配失败，根据spu匹配失败;5. 根据旧有id匹配失败，根据spu匹配成功')
	brand_tm_month_goods_info_id = Column(Integer, comment='brand_tm_month_goods_info表id')
	unit_name = Column(VARCHAR(255), comment='单元名称')
	leaf_category = Column(VARCHAR(25), comment='叶子类目')
	trade_growth_rate2 = Column(DECIMAL(10, 5), comment='销售额环比')
	upload_date = Column(DateTime, comment='上架时间')
	shop_id = Column(VARCHAR(55), comment='店铺id')
	spider_at = Column(DateTime, comment='采集时间')
	keyword = Column(VARCHAR(55), comment='输入关键词')

	@classmethod
	def list(cls, db, month: str, goods_ids, page_no: int = 1, page_size: int = 99999) -> list:
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.trade_money)
		if month:
			db = db.filter(cls.month == month)
		if goods_ids:
			db = db.filter(cls.goods_id.in_(goods_ids))
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def get_unmatch_goods(cls, db, params: dict) -> list:
		month = params.get("month")
		page_size = params.get("page_size")
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.month.label("start_date"), cls.trade_money) \
			.filter(BrandMonthSpuTradeMatchInfo.id == None) \
			.join(BrandMonthSpuTradeMatchInfo,
				  and_(BrandMonthSpuTradeMatchInfo.goods_id == cls.goods_id,
					   BrandMonthSpuTradeMatchInfo.start_date == cls.month,
					   BrandMonthSpuTradeMatchInfo.channel == "JD",
					   BrandMonthSpuTradeMatchInfo.platform == "moojing",
					   ),
				  isouter=True)
		if month:
			db = db.filter(cls.month == month)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		db.query(cls).filter(cls.id == id).update(data)

	# db.commit()

	@classmethod
	def get(cls, db, id):
		return db.query(cls).filter(cls.id == id).first()

	@classmethod
	def list_by_spu_list(cls, db, month: str, spu_list: list, page_no, page_size: int) -> list:
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.month, cls.trade_money).filter(cls.month == month, cls.spu_name.in_(spu_list))
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def insert_data(cls, db, data):
		db.add_all(data)

	@classmethod
	def update_data(cls, db, spider_at, month, keyword, data):
		db.query(cls).filter(
			and_(
				cls.spider_at != spider_at,
				cls.month == month,
				cls.keyword == keyword
			)
		).update(data)

	@classmethod
	def get_group_brand_name_sum_trade(cls, db, month):
		res = db.query(cls.brand_name, func.sum(cls.trade_money)).filter(
			and_(
				cls.month == month
			)
		).group_by(cls.brand_name).all()
		return res


class BrandDyMonthGoodsTradeInfo(BaseModel):
	__tablename__ = 'brand_dy_month_goods_trade_info'
	__table_args__ = (
		Index('udx_webcrawler_brand_dy_month_goods_trade_info_goods_id_month', 'goods_id', 'month', unique=True),
		Index('udx_brand_dy_month_goods_trade_info_goods_id_date', 'goods_id', 'start_date', 'end_date', unique=True)
	)

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	month = Column(DateTime, comment='月份')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	category_name = Column(VARCHAR(25), comment='商品类目(已停用)')
	series_name = Column(VARCHAR(55), comment='商品系列(已停用)')
	sub_category_name = Column(VARCHAR(25), comment='商品子类目(已停用)')
	spu_name = Column(VARCHAR(55), comment='商品SPU(已停用)')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), comment='商品ID')
	goods_url = Column(VARCHAR(512), comment='商品链接')
	pic_url = Column(VARCHAR(512), comment='图片链接')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	shop_id = Column(VARCHAR(55), comment='店铺ID')
	trade_money = Column(DECIMAL(10, 2), comment='交易金额')
	price = Column(DECIMAL(10, 2), comment='商品价格')
	min_price = Column(DECIMAL(10, 2), comment='商品最小价格')
	max_price = Column(DECIMAL(10, 2), comment='商品最大价格')
	volume = Column(Integer, comment='销量')
	author_count = Column(Integer, comment='关联达人')
	aweme_count = Column(Integer, comment='关联视频')
	live_count = Column(Integer, comment='关联直播')
	brand_self_broadcast_amount = Column(DECIMAL(20, 3), nullable=False, comment='品牌自播金额')
	influencer_broadcast_amount = Column(DECIMAL(20, 3), nullable=False, comment='达人播金额')
	store_broadcast_amount = Column(DECIMAL(20, 3), nullable=False, comment='店播金额')
	brand_self_broadcast_volume = Column(DECIMAL(20, 3), nullable=False, comment='品牌自播销量')
	influencer_broadcast_volume = Column(DECIMAL(20, 3), nullable=False, comment='达人播销量')
	store_broadcast_volume = Column(DECIMAL(20, 3), nullable=False, comment='店播销量')
	match_status = Column(Integer,
						  comment='匹配状态，0. 初始值;1. 根据旧有id匹配成功;2. 根据旧有id匹配成功，但name发生改变，根据spu匹配失败;3. 根据旧有id匹配成功，但name发生改变，根据spu匹配成功;4. 根据旧有id匹配失败，根据spu匹配失败;5. 根据旧有id匹配失败，根据spu匹配成功(已停用)')
	brand_tm_month_goods_info_id = Column(Integer, comment='brand_tm_month_goods_info表id(已停用)')
	spider_live_room_time = Column(DateTime, comment='采集直播数据时间')
	goods_card_name = Column(VARCHAR(255), comment='商品卡商品标题')
	goods_card_new_price = Column(VARCHAR(55), comment='商品卡最新到手价')
	goods_card_volume = Column(Integer, comment='商品卡销量')
	goods_card_volume_ratio = Column(DECIMAL(20, 3), comment='商品卡销量占比')
	goods_card_amount = Column(Integer, comment='商品卡销售额')
	goods_card_amount_ratio = Column(DECIMAL(20, 3), comment='商品卡销售额占比')
	goods_image_url = Column(String(555), comment='商品图片')

	@classmethod
	def insert_if_null(cls, db):
		sql = """
		INSERT INTO %s SELECT
		t.* 
		FROM
		%s trade
		RIGHT JOIN %s_tmp t ON trade.goods_id = t.goods_id 
		AND trade.start_date = t.start_date
		AND trade.end_date = t.end_date
		WHERE
		trade.id IS NULL;
		""" % (cls.__tablename__, cls.__tablename__, cls.__tablename__)
		db.execute(sql)

	@classmethod
	def list(
			cls,
			db,
			month: str = None,
			start_date: str = None,
			end_date: str = None,
			page_no: int = 1,
			page_size: int = 100
	) -> list:
		db = db.query(
			cls.id, cls.brand_name, cls.brand_id, cls.goods_name,
			cls.goods_id) \
			.filter(cls.spu_name == None)
		if month:
			db = db.filter(cls.month == month)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		if page_no <= 0:
			page_no = 1
		if page_size <= 0:
			page_no = 100
		res = db.limit(page_size).offset((page_no - 1) * page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def list_by_live_room(
			cls,
			db,
			start_date: str = None,
			end_date: str = None,
			page_no: int = 1,
			page_size: int = 100
	) -> list:
		db = db.query(
			cls.id, cls.goods_id).filter(
				cls.start_date == start_date, cls.end_date == end_date)
		db = db.filter(
			or_(
				cls.spider_live_room_time == None,
				cls.spider_live_room_time <= datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),
			))
		if page_no <= 0:
			page_no = 1
		if page_size <= 0:
			page_no = 100
		res = db.limit(page_size).offset((page_no - 1) * page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def list_by_goods_card(
			cls,
			db,
			start_date: str = None,
			end_date: str = None,
			page_no: int = 1,
			page_size: int = 100
	) -> list:
		db = db.query(cls.id,cls.goods_id).filter(cls.start_date == start_date,cls.end_date == end_date)
		if page_no <= 0:
			page_no = 1
		if page_size <= 0:
			page_no = 100
		res = db.limit(page_size).offset((page_no - 1) * page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def get_unmatch_goods(cls, db, params: dict) -> list:
		start_date = params.get("start_date")
		end_date = params.get("end_date")
		page_size = params.get("page_size")
		db = db.query(cls.id, cls.brand_name,
					  cls.brand_id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.start_date, cls.end_date, cls.trade_money) \
			.filter(BrandMonthSpuTradeMatchInfo.id == None) \
			.join(BrandMonthSpuTradeMatchInfo,
				  and_(BrandMonthSpuTradeMatchInfo.goods_id == cls.goods_id,
					   BrandMonthSpuTradeMatchInfo.start_date == cls.start_date,
					   BrandMonthSpuTradeMatchInfo.end_date == cls.end_date,
					   BrandMonthSpuTradeMatchInfo.channel == "DY",
					   BrandMonthSpuTradeMatchInfo.platform == "chanmama",
					   ),
				  isouter=True)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		# db.query(cls).filter(cls.id == id).update(data)

		# 使用锁实现数据的顺序更新
		# db.execute("SELECT * FROM %s WHERE id = %s FOR UPDATE" % (cls.__tablename__, id))

		# 在需要访问共享资源的代码块中，先获取锁，然后执行操作，最后释放锁
		db.query(cls).filter(cls.id == id).update(data)

	# db.query(cls).filter(cls.id == id).update(data)

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):
		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)
		db.commit()

	@classmethod
	def get(cls, db, id):
		return db.query(cls).filter(cls.id == id).first()

	@classmethod
	def get_by_goods_id(cls, db, goods_id):
		item = db.query(cls).filter(cls.goods_id == goods_id).first()
		return item

	@classmethod
	def get_goods_detail(cls, db, start_time, page_size=500):
		offset = 0
		while True:
			rows = db.query(cls.id, cls.goods_id).filter(cls.start_date == start_time).offset(offset).limit(
				page_size).all()
			if not rows:
				break
			offset += page_size
			yield rows


class BrandSpuManualMatch(BaseModel):
	__tablename__ = 'brand_spu_manual_match'
	__table_args__ = {'comment': 'Spu手工匹配记录表'}

	spu_match_id = Column(Integer, nullable=False, comment='匹配表id')
	spu_name = Column(VARCHAR(255), nullable=False, comment='spu名称')
	refresh_status = Column(Integer, nullable=False, server_default=text("'0'"), comment='刷新状态，0:待刷新，1:刷新成功，2:刷新失败')
	refresh_time = Column(DateTime, comment='刷新时间')

	@classmethod
	def list_by_flush(cls, db) -> list:
		res = db.query(cls).filter(cls.refresh_status == 0).all()
		return res


class MooJingComment(BaseModel):
	__tablename__ = 'das_application_moojing_comment'
	__table_args__ = (
		Index('ind_das_application_moojing_comment_date', 'start_date', 'end_date'),
	)

	spider_at = Column(DateTime, comment='采集时间')
	start_date = Column(DateTime, comment='开始时间')
	end_date = Column(DateTime, comment='结束时间')
	goods_id = Column(VARCHAR(55), nullable=False, index=True, comment='商品ID')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_url = Column(VARCHAR(255), nullable=False, comment='商品url')
	channel = Column(VARCHAR(55), nullable=False, comment='渠道')
	content = Column(Text, comment='评论')
	price = Column(DECIMAL(20, 3), comment='商品价格')
	shop_id = Column(String(55), comment='商品价格')
	shop_name = Column(String(55), comment='店铺名称')
	sku_name = Column(String(55), comment='sku名字')
	sku_id = Column(VARCHAR(55), nullable=False, comment='sku_id')
	title = Column(String(255), comment='商品标题')
	sku_url = Column(VARCHAR(255), comment='sku链接')
	comment_date = Column(Date, index=True, comment='评论日期')

	@classmethod
	def insert(cls, db, data):
		item = cls(**data)
		db.add(item)

	@classmethod
	def list_exists_info(cls, db, start_date, end_date):
		res = db.query(cls).filter(
			and_(cls.start_date == start_date,
				 cls.end_date == end_date
				 )
		).group_by(MooJingComment.goods_id).all()

		return res

	# @classmethod
	# def select_old_comment_time(cls, db, spider_at, start_date, end_date):
	# 	item = db.query(cls).filter(
	# 		and_(
	# 			MooJingComment.start_date >= start_date,
	# 			MooJingComment.end_date <= end_date,
	# 			MooJingComment.spider_at != spider_at,
	# 			MooJingComment.deleted_at.is_(None)
	# 		)
	# 	).all()
	# 	return item

	@classmethod
	def list_group_comment(cls, db, start_date, end_date, goods_ids):
		"""
		获取指定时间，指定goods_id的评论汇总数据
		"""
		group_comment_list = db.query(cls.goods_id, cls.goods_name, cls.goods_url, cls.sku_url, cls.sku_id,
									  func.count(1).label("count")).filter(
			cls.comment_date >= start_date,
			cls.comment_date <= end_date,
			cls.goods_id.in_(goods_ids)
		).group_by(cls.goods_id, cls.goods_name, cls.goods_url, cls.sku_url, cls.sku_id).all()
		res = cls.get_dict_data(group_comment_list)

		# 获取最新的sku_name
		sql = """
		select das_application_moojing_comment.sku_id, sku_name from das_application_moojing_comment
			join (
				select sku_id, max(comment_date) comment_date from das_application_moojing_comment
				where comment_date >= "%s"
				and comment_date <= "%s"
				and deleted_at is null
				GROUP BY sku_id
				) t 
			on das_application_moojing_comment.sku_id = t.sku_id 
			and das_application_moojing_comment.comment_date = t.comment_date
			order by id desc
		""" % (start_date, end_date)
		latest_sku_list = db.execute(sql)
		latest_sku_dict = {}
		for i in latest_sku_list:
			# 同一天可能有多条，靠后的数据更准确，所以使用覆盖逻辑即可
			latest_sku_dict[i[0]] = i[1]

		for i in res:
			i["sku_name"] = latest_sku_dict.get(i["sku_id"])
		return res

	@classmethod
	def update(cls, db, spider_at, start_date, channel, goods_id, end_date, data):
		db.query(cls).filter(
			and_(
				cls.channel == channel,
				cls.spider_at != spider_at,
				cls.comment_date >= start_date,
				cls.comment_date <= end_date,
				cls.goods_id == goods_id,
			)
		).update(data)


class DasApplicationPriceCatDetail(BaseModel):
	__tablename__ = 'das_application_price_cat_detail'
	__table_args__ = ({'comment': '价格猫详情表\\n'})

	spider_at = Column(DateTime, comment='采集时间')
	date_time = Column(Date, comment='价格猫采集时间')
	platform = Column(VARCHAR(55), comment='平台')
	shop_logo = Column(TEXT, comment='店铺LOGO')
	shop_id = Column(VARCHAR(255), comment='店铺ID')
	shop_name = Column(VARCHAR(255), comment='店铺名称')
	egoods_id = Column(VARCHAR(255), comment='商品ID')
	egoods_img_url = Column(TEXT, comment='商品主图URL')
	egoods_name = Column(VARCHAR(255), comment='商品名称')
	egoods_coupons = Column(TEXT, comment='商品券')
	shop_coupons = Column(TEXT, comment='店铺券')
	platform_coupons = Column(TEXT, comment='平台券')
	page_price = Column(VARCHAR(55), comment='页面价')
	floor_price = Column(VARCHAR(55), comment='到手价')
	alter_price = Column(VARCHAR(55), comment='设置监控价格')
	price_status = Column(VARCHAR(255), comment='是否破价')
	egoods_status = Column(VARCHAR(255), comment='商品上下架')
	sku_status = Column(VARCHAR(55), comment='sku上下架')
	sku_id = Column(VARCHAR(255), comment='skuID')
	sku_name = Column(VARCHAR(255), comment='sku名称')
	stock_num = Column(VARCHAR(55), comment='库存状态')
	egoods_url = Column(TEXT, comment='商品跳转链接')

	@classmethod
	def list_by_latest(cls, db, unit_ids) -> list:
		sql = """
			select das_application_price_cat_detail.sku_id, floor_price from das_application_price_cat_detail
			join 
				(
				select sku_id, max(date_time) date_time from das_application_price_cat_detail
				where sku_id in (%s) 
				GROUP BY sku_id
				) t
			on das_application_price_cat_detail.sku_id = t.sku_id
			and das_application_price_cat_detail.date_time = t.date_time
		""" % ','.join(unit_ids)
		res = db.execute(sql)
		return res

	@classmethod
	def list_by_unit_ids(cls, db, unit_ids):
		res = db.query(cls.sku_id, cls.floor_price).filter(cls.sku_id.in_(unit_ids)).all()
		return res


class BrandJdMonthSkuTradeInfo(BaseModel):
	__tablename__ = 'brand_jd_month_sku_trade_info'
	__table_args__ = (
		Index('udx_brand_jd_month_sku_trade_info_date_unit_id', 'start_date', 'end_date', 'unit_id', 'goods_id',
			  "deleted_at", unique=True),
		{'comment': '竞对品牌 每月京东sku关系记录表'}
	)

	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	unit_name = Column(VARCHAR(255), comment='渠道sku名称')
	unit_id = Column(VARCHAR(55), nullable=False, comment='渠道skuid')
	unit_url = Column(VARCHAR(255), comment='渠道sku链接')
	system_sku_name = Column(VARCHAR(255), nullable=False, comment='系统sku名称')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	quantity = Column(Integer, nullable=False, server_default=text("'1'"), comment='数量')

	@classmethod
	def insert_by_dict(cls, db, data):
		item = cls(**data)
		db.add(item)
		# db.commit()
		db.flush()
		return item

	@classmethod
	def list(cls, db, start_date: str, end_date: str, page_no=1, page_size=99999) -> list:
		db = db.query(cls.goods_id, cls.unit_id) \
			.filter(cls.start_date == start_date, cls.end_date == end_date)
		res = db.limit(page_size).all()
		return res

	@classmethod
	def get_build_record(cls, db):
		"""

		"""
		res = db.query(cls.unit_name, cls.system_sku_name).filter(cls.unit_name != "",
																  cls.unit_name is not None).group_by(cls.unit_name,
																									  cls.system_sku_name).all()
		return cls.get_dict_data(res)

	@classmethod
	def get_unmatch_sku(cls, db, params: dict) -> list:
		start_date = params.get("start_date")
		end_date = params.get("end_date")
		page_size = params.get("page_size")
		db = db.query(cls.id, cls.goods_name, cls.goods_id, cls.goods_url,
					  cls.unit_name, cls.unit_id, cls.unit_url,
					  cls.system_sku_name, cls.quantity,
					  cls.start_date, cls.end_date) \
			.filter(BrandMonthSkuTradeMatchInfo.id == None) \
			.join(BrandMonthSkuTradeMatchInfo,
				  and_(BrandMonthSkuTradeMatchInfo.goods_id == cls.goods_id,
					   BrandMonthSkuTradeMatchInfo.start_date == cls.start_date,
					   BrandMonthSkuTradeMatchInfo.end_date == cls.end_date,
					   BrandMonthSkuTradeMatchInfo.channel == "JD",
					   BrandMonthSkuTradeMatchInfo.platform == "moojing",
					   BrandMonthSkuTradeMatchInfo.deleted_at.is_(None),
					   ),
				  isouter=True)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def delete_by_manual_spu(cls, db, goods_id, start_date, end_date):
		return db.query(cls).filter(cls.goods_id == goods_id, cls.start_date == start_date, cls.end_date == end_date) \
			.update({"deleted_at": datetime.datetime.now(), "remark": "spu发生变更，不再监控"})


class BrandMonthSkuTradeMatchInfo(BaseModel):
	__tablename__ = 'brand_month_sku_trade_match_info'
	__table_args__ = (
		Index('ind_brand_month_sku_trade_match_info_channel_platform', 'channel', 'platform'),
		Index('idx_brand_month_sku_trade_match_info_date_unit_id', 'start_date', 'end_date', 'unit_id', 'goods_id'),
		{'comment': '竞对品牌 月sku销售匹配数据表'}
	)

	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	unit_name = Column(VARCHAR(255), comment='单元名称')
	unit_id = Column(VARCHAR(55), nullable=False, comment='单元id')
	unit_url = Column(VARCHAR(255), comment='单元链接')
	system_sku_name = Column(VARCHAR(255), nullable=False, comment='渠道sku名称')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	sku_name = Column(VARCHAR(255), index=True, comment='sku名称')
	match_status = Column(Integer,
						  comment='匹配状态，0. 初始值 1. 匹配成功 2. 根据旧有id匹配成功，但name发生改变，根据spu匹配失败 3. 根据旧有id匹配成功，但name发生改变，根据spu匹配成功 4. 根据旧有id匹配失败，根据spu匹配失败 5. 根据旧有id匹配失败，根据spu匹配成功 10. 人工匹配 6. 由于spu变更，导致匹配关系被清空')
	trade_money = Column(DECIMAL(20, 3), comment='交易金额')
	price = Column(DECIMAL(20, 3), comment='商品单价')
	sales = Column(DECIMAL(20, 3), comment='销售额')
	comment_count = Column(Integer, comment='评论数量')
	quantity = Column(Integer, nullable=False, server_default=text("'1'"), comment='数量')
	channel = Column(VARCHAR(25), nullable=False, comment='渠道，如TM、JD、DY')
	platform = Column(VARCHAR(55), nullable=False, comment='平台，如chanmama、sycm、moojing')
	is_new = Column(Integer, comment='是否是新出现的id')

	@classmethod
	def get_spu_by_latest(cls, db, channel, platform):
		sql = """
            select brand_month_sku_trade_match_info.unit_id, system_sku_name, sku_name, match_status, quantity from brand_month_sku_trade_match_info 
            JOIN (
            select unit_id, max(`end_date`) max_end_date from brand_month_sku_trade_match_info
            where deleted_at is null
            and channel = '%s'
            and platform = '%s'
            GROUP BY unit_id
            ) t on brand_month_sku_trade_match_info.unit_id = t.unit_id and brand_month_sku_trade_match_info.end_date = t.max_end_date
            where deleted_at is null
        """ % (channel, platform)
		res = db.execute(sql)
		return cls.get_dict_data(res)

	@classmethod
	def insert_by_dict(cls, db, data):
		item = cls(**data)
		db.add(item)
		# db.commit()
		db.flush()
		return item

	@classmethod
	def list_for_calc_money(cls, db, channel, platform):
		res = db.query(cls.goods_id, cls.start_date, cls.end_date).filter(cls.trade_money.is_(None),
																		  cls.channel == channel,
																		  cls.platform == platform,
																		  cls.sku_name != '').all()
		# goods_ids = [i[0] for i in goods_id_res]
		# res = db.query(cls.id, cls.goods_id, cls.price, cls.quantity, cls.comment_count, cls.start_date, cls.end_date) \
		# 	.filter(cls.goods_id.in_(goods_ids))

		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		return db.query(cls).filter(cls.id == id).update(data)

	@classmethod
	def update_by_goods_id(cls, db, goods_id, start_date, end_date, channel, platform, data):
		return db.query(cls).filter(cls.goods_id == goods_id, cls.start_date == start_date, cls.end_date == end_date,
									cls.channel == channel, cls.platform == platform) \
			.update(data)

	@classmethod
	def delete_by_manual_spu(cls, db, goods_id, start_date, end_date, channel, platform):
		db.query(cls).filter(cls.goods_id == goods_id, cls.start_date == start_date, cls.end_date == end_date,
							 cls.channel == channel, cls.platform == platform) \
			.update({"deleted_at": datetime.datetime.now(), "remark": "spu发生变更，不再监控"})

		if channel == "TM" and platform == "sycm":
			# BrandTmMonthUnitCommentInfo.delete_by_manual_spu(db, goods_id, start_date, end_date)
			BrandTmMonthSkuTradeInfo.delete_by_manual_spu(db, goods_id, start_date, end_date)
		elif channel == "JD" and platform == "moojing":
			BrandJdMonthSkuTradeInfo.delete_by_manual_spu(db, goods_id, start_date, end_date)

	@classmethod
	def list_by_sku_name(cls, db, sku_name):
		res = db.query(cls.sku_name, cls.unit_id).filter(cls.sku_name == sku_name).all()
		return res

	@classmethod
	def list_for_refresh_price(cls, db):
		res = db.query(cls.id, cls.sku_name).filter(cls.price.is_(None), cls.sku_name != '').all()
		return res

	@classmethod
	def update_for_sku_name_price(cls, db, channel, platform, sku_name, price):
		# 只更新上月的数据
		now = datetime.date.today()
		start_date = (datetime.datetime(now.year, now.month, 1) - datetime.timedelta(days=1)).replace(day=1)
		end_date = datetime.datetime(now.year, now.month, 1) - datetime.timedelta(days=1) + datetime.timedelta(hours=23,
																											   minutes=59,
																											   seconds=59)
		res = db.query(cls).filter(cls.channel == channel, cls.platform == platform, cls.start_date == start_date,
								cls.end_date == end_date, cls.sku_name == sku_name)\
			.update({"price": price, "trade_money": None})
		return res

	@classmethod
	def update_for_refresh_price(cls, db, channel, platform, start_date, end_date, is_all):
		if is_all:
			sql = text("""
			update brand_month_sku_trade_match_info
			join sku_list on brand_month_sku_trade_match_info.sku_name = sku_list.sku_name
			set brand_month_sku_trade_match_info.trade_money = NULL
			where brand_month_sku_trade_match_info.deleted_at is null 
			and brand_month_sku_trade_match_info.channel = :channel
			and brand_month_sku_trade_match_info.platform = :platform
			and sku_list.month = :start_date
			and (brand_month_sku_trade_match_info.price is null or brand_month_sku_trade_match_info.price != sku_list.price)
			""").bindparams(channel=channel, platform=platform)
			res = db.execute(sql)
			db.commit()
			sql = text("""
			update brand_month_sku_trade_match_info
			join sku_list on brand_month_sku_trade_match_info.sku_name = sku_list.sku_name
			set brand_month_sku_trade_match_info.price = sku_list.price
			where brand_month_sku_trade_match_info.deleted_at is null
			and brand_month_sku_trade_match_info.channel = :channel
			and brand_month_sku_trade_match_info.platform = :platform
			and sku_list.month = :start_date
			""").bindparams(channel=channel, platform=platform)
			res = db.execute(sql)
			db.commit()
		else:
			sql = text("""
			update brand_month_sku_trade_match_info
			join sku_list on brand_month_sku_trade_match_info.sku_name = sku_list.sku_name
			set brand_month_sku_trade_match_info.trade_money = NULL
			where brand_month_sku_trade_match_info.start_date = :start_date and brand_month_sku_trade_match_info.end_date = :end_date
			and brand_month_sku_trade_match_info.deleted_at is null 
			and (brand_month_sku_trade_match_info.price is null or brand_month_sku_trade_match_info.price != sku_list.price)
			and brand_month_sku_trade_match_info.channel = :channel
			and brand_month_sku_trade_match_info.platform = :platform
			and sku_list.month = :start_date
			""")
			sql = sql.bindparams(start_date=start_date, end_date=end_date, channel=channel, platform=platform)
			db.execute(sql)
			db.commit()
			sql = text("""
			update brand_month_sku_trade_match_info
			join sku_list on brand_month_sku_trade_match_info.sku_name = sku_list.sku_name
			set brand_month_sku_trade_match_info.price = sku_list.price
			where brand_month_sku_trade_match_info.start_date = :start_date and brand_month_sku_trade_match_info.end_date = :end_date
			and brand_month_sku_trade_match_info.deleted_at is null
			and brand_month_sku_trade_match_info.channel = :channel
			and brand_month_sku_trade_match_info.platform = :platform
			and sku_list.month = :start_date
			""")
			sql = sql.bindparams(start_date=start_date, end_date=end_date, channel=channel, platform=platform)
			res = db.execute(sql)
			db.commit()
		return res.rowcount

	@classmethod
	def list_by_goods_ids(cls, db, goods_ids, start_date, end_date):
		res = db.query(cls.id, cls.goods_id, cls.price, cls.quantity, cls.sales, cls.start_date, cls.end_date) \
			.filter(cls.start_date == start_date, cls.end_date == end_date, cls.goods_id.in_(goods_ids)).all()
		return cls.get_dict_data(res)

	@classmethod
	def delete_by_unit_id(cls, db, start_date, end_date, unit_id ):
		return db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.unit_id == unit_id,
				# cls.unit_name == unit_name
			)
		).update({"deleted_at": datetime.datetime.now(), "remark": "unit发生变更"})

class BrandTmMonthUnitCommentInfo(BaseModel):
	__tablename__ = 'brand_tm_month_unit_comment_info'
	__table_args__ = (
		Index('udx_brand_tm_month_unit_comment_info_date_unit_id', 'start_date', 'end_date', 'unit_id', 'goods_id',
			  "deleted_at", unique=True),
		{'comment': '竞对品牌 每月天猫单元记录关系表'}
	)

	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	goods_url = Column(String(255), comment='商品链接')
	unit_name = Column(String(255), comment='渠道sku名称')
	unit_id = Column(VARCHAR(55), nullable=False, comment='渠道skuid')
	unit_url = Column(String(255), comment='渠道sku链接')
	comment_count = Column(Integer, nullable=False, comment='评论数量')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	spider_at = Column(DateTime, comment='采集时间')
	# goods_title = Column(String(255), comment='商品标题')
	sales = Column(DECIMAL(20, 2), nullable=False, server_default=text("'0.00'"), comment='销售额')
	sold = Column(Integer, comment='销量')
	mj_brand_id = Column(String(55), comment='品牌id')
	brand_name = Column(String(55), comment='品牌名称')
	shop_id = Column(String(55), comment='店铺id')
	shop_name = Column(String(55), comment='店铺名称')
	shop_type = Column(String(55), comment='店铺类型')
	leaf_name = Column(String(55), comment='叶子类目名称')
	price = Column(DECIMAL(20, 2), nullable=False, server_default=text("'0.00'"), comment='价格')
	img_url = Column(String(555), comment='图片链接')
	keyword= Column(String(55), comment='输入关键词')

	@classmethod
	def list_by_spu_list(cls, db, start_date: str, end_date: str, spu_list: list, channel: str, platform: str,
						 page_no=1, page_size=99999) -> list:
		goods_list = BrandMonthSpuTradeMatchInfo.list_by_spu_list(db, start_date, end_date, spu_list, channel, platform)
		goods_ids = [i["goods_id"] for i in goods_list]
		db = db.query(cls.id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.unit_name, cls.unit_id, cls.unit_url,
					  cls.start_date, cls.end_date, cls.comment_count).filter(cls.start_date == start_date,
																			  cls.end_date == end_date,
																			  cls.goods_id.in_(goods_ids),
																			  cls.unit_id != 0,
																			  )
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def delete_by_id(cls, db, id):
		return db.query(cls).filter(cls.id == id).update({"deleted_at": datetime.datetime.now(),"remark": "unit发生变更"})
	@classmethod
	def list_new_or_old_moonjing_list(cls,db,start_date: str, end_date: str,
						 page_no=1, page_size=99999) -> tuple[ list, list ]:

		# goods_list = BrandMonthSpuTradeMatchInfo.list_by_spu_list(db, start_date, end_date, spu_list, channel, platform)
		# goods_ids = [i["goods_id"] for i in goods_list]
		db = db.query(cls.id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.unit_name, cls.unit_id, cls.unit_url,
					  cls.start_date, cls.end_date, cls.comment_count).filter(cls.start_date == start_date,
																			  cls.end_date == end_date,
																			  cls.goods_name != None,
																			  cls.deleted_at == None,
																			  cls.unit_id!=0,
																			  )
		new_db=db.filter(cls.keyword != None)
		old_db = db.filter(cls.keyword == None)
		new_res=new_db.limit(page_size).all()
		old_res=old_db.limit(page_size).all()

		return cls.get_dict_data(new_res),cls.get_dict_data(old_res)

	@classmethod
	def insert_data(cls, db, data):
		db.add_all(data)

	@classmethod
	def get_unit_data(cls, db, start_date, end_date,page_size=99999):
		item = db.query(cls.id, cls.goods_name,cls.goods_id, cls.goods_url, cls.unit_name, cls.unit_id, cls.unit_url,
						cls.start_date, cls.end_date,).filter(and_(cls.start_date == start_date, cls.end_date == end_date)
															  ).limit(page_size).all()
		return cls.get_dict_data(item)

	@classmethod
	def delete_by_unit_id_name(cls,db,start_date, end_date,unit_id,unit_name,):
		return db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.unit_id == unit_id,
				cls.unit_name == unit_name
			)
		).update({"deleted_at": datetime.datetime.now(),"remark": "unit发生变更"})

	@classmethod
	def insert(cls, db, item):
		db.add(item)
		db.flush()
		return item

	@classmethod
	def get(cls, db, start_date, end_date, unit_id):
		res = db.query(cls).filter(cls.start_date == start_date, cls.end_date == end_date,
								   cls.unit_id == unit_id).first()
		return res

	@classmethod
	def list(cls, db, start_date, end_date, page_no=1, page_size=99999):
		db = db.query(cls.id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.unit_name, cls.unit_id, cls.unit_url,
					  cls.start_date, cls.end_date, cls.sales).filter(cls.start_date == start_date,
																			  cls.end_date == end_date)
		res = db.limit(page_size).all()
		return res

	@classmethod
	def delete_by_manual_spu(cls, db, goods_id, start_date, end_date):
		return db.query(cls).filter(cls.goods_id == goods_id, cls.start_date == start_date, cls.end_date == end_date) \
			.update({"deleted_at": datetime.datetime.now(), "remark": "spu发生变更，不再监控"})


class BrandTmMonthSkuTradeInfo(BaseModel):
	__tablename__ = 'brand_tm_month_sku_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_sku_trade_info_date_unit_id', 'start_date', 'end_date', 'unit_id', 'goods_id',
			  "deleted_at", unique=True),
		{'comment': '竞对品牌 每月天猫sku记录关系表'}
	)

	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	unit_name = Column(VARCHAR(255), comment='渠道sku名称')
	unit_id = Column(VARCHAR(55), nullable=False, comment='渠道skuid')
	unit_url = Column(VARCHAR(255), comment='渠道sku链接')
	system_sku_name = Column(VARCHAR(255), nullable=False, comment='渠道sku名称')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	quantity = Column(Integer, nullable=False, server_default=text("'1'"), comment='数量')

	@classmethod
	def list(cls, db, start_date: str, end_date: str, page_no=1, page_size=99999) -> list:
		db = db.query(cls.goods_id, cls.unit_id) \
			.filter(cls.start_date == start_date, cls.end_date == end_date)
		res = db.limit(page_size).all()
		return res

	@classmethod
	def get_build_record(cls, db):
		"""

		"""
		res = db.query(cls.unit_name, cls.system_sku_name).filter(cls.unit_name != "",
																  cls.unit_name is not None).group_by(cls.unit_name,
																									  cls.system_sku_name).all()
		return cls.get_dict_data(res)

	@classmethod
	def get_unmatch_sku(cls, db, params: dict) -> list:
		start_date = params.get("start_date")
		end_date = params.get("end_date")
		page_size = params.get("page_size")
		db = db.query(cls.id, cls.goods_name,
					  cls.goods_id, cls.goods_url,
					  cls.unit_name, cls.unit_id, cls.unit_url,
					  cls.system_sku_name, cls.quantity,
					  cls.start_date, cls.end_date) \
			.filter(BrandMonthSkuTradeMatchInfo.id == None) \
			.join(BrandMonthSkuTradeMatchInfo,
				  and_(BrandMonthSkuTradeMatchInfo.unit_id == cls.unit_id,
					   BrandMonthSkuTradeMatchInfo.start_date == cls.start_date,
					   BrandMonthSkuTradeMatchInfo.end_date == cls.end_date,
					   BrandMonthSkuTradeMatchInfo.channel == "TM",
					   BrandMonthSkuTradeMatchInfo.platform == "sycm",
					   BrandMonthSkuTradeMatchInfo.deleted_at.is_(None),
					   ),
				  isouter=True)
		if start_date:
			db = db.filter(cls.start_date == start_date)
		if end_date:
			db = db.filter(cls.end_date == end_date)
		res = db.limit(page_size).all()
		return cls.get_dict_data(res)

	@classmethod
	def delete_by_manual_spu(cls, db, goods_id, start_date, end_date):
		return db.query(cls).filter(cls.goods_id == goods_id, cls.start_date == start_date, cls.end_date == end_date) \
			.update({"deleted_at": datetime.datetime.now(), "remark": "spu发生变更，不再监控"})

	@classmethod
	def delete_by_unit_id(cls, db, start_date, end_date, unit_id,  ):
		return db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.unit_id == unit_id,
				# cls.unit_name == unit_name
			)
		).update({"deleted_at": datetime.datetime.now(), "remark": "unit发生变更"})

	@classmethod
	def update(cls, db, id, data):
		return db.query(cls).filter(cls.id == id).update(data)

class BrandSkuManualMatch(BaseModel):
	__tablename__ = 'brand_sku_manual_match'
	__table_args__ = {'comment': 'Sku手工匹配记录表'}

	sku_match_id = Column(Integer, nullable=False, comment='匹配表id')
	sku_name = Column(VARCHAR(255), nullable=False, comment='sku名称')
	is_add = Column(Integer, nullable=False, server_default=text("'0'"), comment='是否添加')
	is_deleted = Column(Integer, nullable=False, server_default=text("'0'"), comment='是否删除')
	quantity = Column(Integer, nullable=False, server_default=text("'1'"), comment='数量')
	refresh_status = Column(Integer, nullable=False, server_default=text("'0'"), comment='刷新状态，0:待刷新，1:刷新成功，2:刷新失败')
	refresh_time = Column(DateTime, comment='刷新时间')

	@classmethod
	def list_by_flush(cls, db):
		res = db.query(cls).filter(cls.refresh_status == 0).all()
		return res

	def __repr__(self):
		return self.id.__str__()


class SkuList(BaseModel):
	__tablename__ = 'sku_list'
	__table_args__ = {'comment': 'sku资料表'}

	brand_name = Column(String(55), comment='品牌名称')
	spu_name = Column(VARCHAR(55), comment='商品SPU')
	sku_name = Column(VARCHAR(55), unique=True, comment='商品SKU')
	goods_name = Column(VARCHAR(255), comment='商品名称')
	taste = Column(VARCHAR(255), comment='口味')
	standard = Column(VARCHAR(255), comment='执行标准')
	min_month_age = Column(VARCHAR(255), comment='最小适用月龄')
	specification = Column(VARCHAR(255), comment='规格')
	sell_keyword = Column(VARCHAR(255), comment='卖点关键词')
	alias = Column(VARCHAR(255), comment='别名')
	price = Column(DECIMAL(20, 3), comment='价格')
	month = Column(DateTime, comment='月份')

	@classmethod
	def list_by_price(cls, db, month=None):
		db = db.query(cls.sku_name, cls.price, cls.month)
		if month:
			res = db.filter(cls.month == month).all()
		else:
			res = db.filter(cls.month != None).all()
		return cls.get_dict_data(res)

	@classmethod
	def list(cls, db):
		res = db.query(cls).filter(cls.month != None).all()
		return res

	@classmethod
	def update_by_alias(cls, db, alias, data):
		return db.query(cls).filter(cls.alias == alias).update(data)


class BrandDyGoodsLiveRoom(BaseModel):
	__tablename__ = 'brand_dy_goods_live_room'
	__table_args__ = (
		Index('brand_dy_goods_live_room_goods_room_id', 'goods_id', 'room_id', unique=True),
		{'comment': '抖音商品直播数据表'}
	)

	goods_id = Column(String(55), comment='商品id')
	room_title = Column(String(255), comment='直播间标题')
	room_id = Column(String(55), comment='直播间id')
	begin_time = Column(DateTime, comment='直播开始时间')
	duration = Column(Integer, comment='直播持续时间(秒)')
	room_finish_time = Column(DateTime, comment='直播结束时间')
	introduce_duration = Column(Integer, comment='讲解时长')
	nickname = Column(String(255), comment='达人名称')
	unique_id = Column(String(55), comment='达人抖音号')
	follower_count = Column(Integer, comment='达人粉丝数')
	total_user = Column(Integer, comment='观看人次')
	user_value = Column(DECIMAL(10, 5), comment='本场uv')
	live_price = Column(DECIMAL(20, 3), comment='价格')
	volume = Column(Integer, comment='本品销量')
	amount = Column(DECIMAL(20, 3), comment='本品销售额')
	click_percent = Column(DECIMAL(10, 5), comment='点击率')
	conversion_percent = Column(DECIMAL(10, 5), comment='转化率')
	author_id = Column(String(55), comment='作者id')

	@classmethod
	def list(
			cls,
			db,
			start_date: str = None,
			end_date: str = None,
			page_no: int = 1,
			page_size: int = 100
	) -> list:
		db = db.query(
			cls.id, cls.goods_id, cls.room_id)
		if start_date:
			db = db.filter(cls.begin_time >= start_date)
		if end_date:
			db = db.filter(cls.begin_time <= end_date)
		if page_no <= 0:
			page_no = 1
		if page_size <= 0:
			page_no = 100
		res = db.limit(page_size).offset((page_no - 1) * page_size).all()
		return res


class BrandTmShopTradeInfo(BaseModel):
	__abstract__ = True
	__table_args__ = ({'comment': '天猫各品牌店铺交易数据基础表\\n'})

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	shop_url = Column(VARCHAR(255), comment='店铺链接')
	shop_id = Column(VARCHAR(55), comment='店铺ID')
	trade_money = Column(Integer, comment='交易金额')
	visitors_nums = Column(Integer, comment='访客人数')
	growth_rate = Column(DECIMAL(10, 5), comment='增长幅度')
	search_nums = Column(Integer, comment='搜索人数')
	search_rate = Column(DECIMAL(10, 5), comment='搜索占比')
	buyer_nums = Column(Integer, comment='买家数')
	payment_conversion_rate = Column(DECIMAL(10, 5), comment='支付转化率')
	atv = Column(DECIMAL(20, 3), comment='客单价')
	uv_value = Column(DECIMAL(20, 3), comment='UV价值')
	brand_tm_category_id = Column(Integer, comment='商品类目id')
	payment_money_rank = Column(INTEGER(11), comment='支付金额排名')

	@classmethod
	def insert_brand_id(cls, db):
		sql = """
	        update %s_tmp t
	        join brand_channel_info on t.brand_name = brand_channel_info.brand_name
	        set t.brand_id = brand_channel_info.brand_id
	        where brand_channel_info.channel = 'TM'
	        and brand_channel_info.deleted_at is null;
	        """ % cls.__tablename__
		db.execute(sql)

	@classmethod
	def insert_if_null(cls, db):
		sql = """
		INSERT INTO %s SELECT
		t.* 
		FROM
			%s trade
			RIGHT JOIN %s_tmp t ON trade.shop_id = t.shop_id 
			AND trade.start_date = t.start_date
			AND trade.end_date = t.end_date
			AND trade.brand_tm_category_id=t.brand_tm_category_id
			AND trade.brand_id=t.brand_id
		WHERE
			trade.id IS NULL
			AND t.brand_id != "";
		""" % (cls.__tablename__, cls.__tablename__, cls.__tablename__)
		db.execute(sql)

	@classmethod
	def truncate_table(cls, db):
		sql = """
	        TRUNCATE TABLE %s_tmp;
	        """ % cls.__tablename__
		db.execute(sql)

	@classmethod
	def insert_shop_info(cls, db):
		# 取最新店铺信息数据,并找出新的店铺信息,插入到shop_list表中
		sql = """
		INSERT INTO shop_list (shop_name, shop_id)
		SELECT DISTINCT b.shop_name, b.shop_id
		FROM %s b
		JOIN (
			SELECT shop_id, MAX(start_date) AS max_start_date
			FROM %s
			GROUP BY shop_id
			)max_dates
		ON b.start_date = max_dates.max_start_date
		AND b.shop_id = max_dates.shop_id
		LEFT JOIN shop_list s
		ON b.shop_id = s.shop_id WHERE s.id IS NULL
		""" % (cls.__tablename__, cls.__tablename__)
		res = db.execute(sql)
		db.commit()
		logger.info(f"brand_goods_into shop_list表新增{res.rowcount}条店铺信息")
		sql = """
		UPDATE shop_list
		JOIN %s
		ON
		shop_list.shop_id=%s.shop_id
		SET shop_list.shop_url=%s.shop_url
		WHERE
		%s.shop_url !=''
		and shop_list.shop_url IS NULL
		""" % (cls.__tablename__, cls.__tablename__, cls.__tablename__, cls.__tablename__)
		res = db.execute(sql)
		db.commit()
		return res

	@classmethod
	def list_by_items_info_list(cls, db, start_date: str, end_date: str, category_id: int) -> list:
		res = db.query(cls.id, cls.shop_id, cls.trade_money,
						cls.payment_money_rank, cls.growth_rate, cls.brand_id).filter(
			cls.start_date == start_date, cls.end_date == end_date, cls.brand_tm_category_id == category_id).all()
		return cls.get_dict_data(res)

	@classmethod
	def update(cls, db, id, data):
		db.query(cls).filter(cls.id == id).update(data)


class BrandTmWeekShopTradeInfo(BrandTmShopTradeInfo):
	__tablename__ = 'brand_tm_week_shop_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_shop_trade_info_shop_id_brand_name_date_cate', 'shop_id', 'start_date', 'end_date',
			  'brand_name','brand_tm_category_id', unique=True),
	)


class BrandTmMonthShopTradeInfo(BrandTmShopTradeInfo):
	__tablename__ = 'brand_tm_month_shop_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_shop_trade_info_shop_id_brand_name_date_cate', 'shop_id', 'start_date', 'end_date',
			  'brand_name', 'brand_tm_category_id', unique=True),
	)
	month = Column(DateTime, comment='月份')


class BrandTmDayShopTradeInfo(BrandTmShopTradeInfo):
	__tablename__ = 'brand_tm_day_shop_trade_info'
	__table_args__ = (
		Index('udx_brand_tm_month_shop_trade_info_shop_id_brand_name_date_cate', 'shop_id', 'start_date', 'end_date',
			  'brand_name', 'brand_tm_category_id', unique=True),
	)


class DasApplicationMoojingUnitTrade(BaseModel):
	__tablename__ = 'das_application_moojing_unit_trade'
	__table_args__ = (
		Index('ind_das_application_moojing_unit_trade_comment_date', 'start_date', 'end_date'),
		{'comment': '魔镜单元级别销售数据'}
	)

	goods_name = Column(VARCHAR(255), comment='商品名称')
	goods_id = Column(VARCHAR(55), nullable=False, index=True, comment='商品ID')
	goods_url = Column(VARCHAR(255), comment='商品链接')
	unit_name = Column(VARCHAR(255), comment='渠道sku名称')
	unit_id = Column(VARCHAR(55), nullable=False, index=True, comment='渠道skuid')
	unit_url = Column(VARCHAR(255), comment='渠道sku链接')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	spider_at = Column(DateTime, comment='采集时间')
	sales = Column(DECIMAL(20, 3), comment='销售额')
	sold = Column(Integer, comment='销量')
	mj_brand_id = Column(VARCHAR(55), comment='品牌id')
	brand_name = Column(VARCHAR(55), comment='品牌名称')
	shop_id = Column(VARCHAR(55), comment='店铺id')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	shop_type = Column(VARCHAR(55), comment='店铺类型')
	leaf_name = Column(VARCHAR(55), comment='叶子类目名称')
	price = Column(DECIMAL(20, 3), comment='价格')
	img_url = Column(VARCHAR(555), comment='图片链接')
	keyword = Column(VARCHAR(55), comment='输入关键词')

	@classmethod
	def update_data(cls, db,spider_at, start_date, end_date, keyword,data):
		db.query(cls).filter(
			and_(
				cls.spider_at != spider_at,
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.keyword == keyword
			)
		).update(data)

	@classmethod
	def insert_data(cls, db, data):
		db.add_all(data)

	@classmethod
	def get_unit_data(cls, db, start_date, end_date, page_size=99999):
		res = db.query(cls)\
			.filter(and_(
			cls.start_date == start_date, cls.end_date == end_date))\
			.limit(page_size).all()
		return res

	def to_dict(self):
		return {
			'id': self.id,
			'created_at': self.created_at,
			'updated_at': self.updated_at,
			'deleted_at': self.deleted_at,
			'remark': self.remark,
			'goods_name': self.goods_name,
			'goods_id': self.goods_id,
			'goods_url': self.goods_url,
			'unit_name': self.unit_name,
			'unit_id': self.unit_id,
			'unit_url': self.unit_url,
			'start_date': self.start_date,
			'end_date': self.end_date,
			'spider_at': self.spider_at,
			'sales': self.sales,
			'sold': self.sold,
			'mj_brand_id': self.mj_brand_id,
			'brand_name': self.brand_name,
			'shop_id': self.shop_id,
			'shop_name': self.shop_name,
			'shop_type': self.shop_type,
			'leaf_name': self.leaf_name,
			'price': self.price,
			'img_url': self.img_url,
			'keyword': self.keyword
		}


class BrandDyTalentGoodsVideo(BaseModel):
	__tablename__ = 'brand_dy_talent_goods_video'
	__table_args__ = (
		Index('udx_brand_dy_talent_video_goods_aweme_id_date', 'start_date', 'end_date', 'aweme_id', unique=True),
		{'comment': '抖音达人视频带货数据表'}
	)
	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	dy_id = Column(VARCHAR(55), nullable=False, comment='抖音id')
	aweme_title = Column(String(255), comment='视频标题')
	aweme_url = Column(String(555), comment='视频链接')
	goods_name = Column(VARCHAR(255), comment='关联商品标题')
	goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
	nickname = Column(String(255), comment='达人名称')
	author_id = Column(String(55), comment='达人id')
	aweme_create_time = Column(DateTime, comment='视频发布时间')
	digg_count = Column(Integer, comment='点赞数')
	comment_count = Column(Integer, comment='评论数')
	share_count = Column(Integer, comment='转发数')
	volume = Column(Integer, comment='销量')
	amount = Column(Integer, comment='销售额')
	aweme_id = Column(String(55), comment='视频id')
	follower_count = Column(Integer, comment='达人粉丝数')
	duration = Column(Integer, comment='视频时间(秒)')
	play_count = Column(Integer, comment='播放量')
	category_name = Column(VARCHAR(55), comment='带货分类名')

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):

		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)
		db.commit()


class BrandTalentBdInfo(BaseModel):
	__tablename__ = 'brand_talent_bd_info'
	__table_args__ = (
		Index('udx_brand_talent_bd_info_nick_id_date', 'start_date', 'end_date', 'nick_id', unique=True),
		Index('udx_brand_talent_bd_info_nick_id_delete_date', 'deleted_at', 'start_date', 'end_date', 'nick_id',
			unique=True),
		{'comment': '抖音达人与BD关系表'}
	)

	start_date = Column(DateTime, comment='开始时间')
	end_date = Column(DateTime, comment='结束时间')
	nick_name = Column(String(255), comment='达人名称')
	nick_id = Column(String(55), comment='达人ID')
	user_name = Column(String(55), comment='跟进媒介')
	cooperate_status = Column(VARCHAR(55), comment='状态')
	mcn_name = Column(VARCHAR(55), comment='MCN机构')
	add_time = Column(DateTime, comment='绑定时间')
	platform = Column(VARCHAR(55), comment='社媒平台')
	order_amount = Column(DECIMAL(20, 3), comment='成交金额')
	order_volume = Column(Integer, comment='支付销量')
	goods_num = Column(Integer, comment='成交商品')

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):

		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)
		db.commit()


class BrandSampleOrder(BaseModel):
	__tablename__ = 'brand_sample_order'
	__table_args__ = (
		Index('udx_brand_sample_order_sample_id', 'order_id', 'sample_time', 'goods_id', unique=True),
		Index('udx_brand_sample_order_goods_id_delete_date', 'start_date', 'end_date', 'order_id', 'goods_id',
			  'deleted_at', unique=True),
		{'comment': '馋圈圈样品单数据'}
	)

	start_date = Column(DateTime, nullable=False, comment='开始时间')
	end_date = Column(DateTime, nullable=False, comment='结束时间')
	order_id = Column(VARCHAR(55), nullable=False, comment='订单ID')
	regiment_name = Column(String(55), comment='团长名称(团长选项特有)')
	nick_name = Column(String(55), comment='达人昵称')
	nick_dy_id = Column(String(55), comment='达人抖音号')
	follower_count = Column(Integer, comment='达人粉丝数')
	goods_type = Column(String(55), comment='带货类型')
	volume_30 = Column(Integer, comment='近30日销量')
	sample_time = Column(DateTime, comment='申样时间')
	goods_name = Column(String(55), comment='领样商品')
	shop_name = Column(VARCHAR(55), comment='店铺名称')
	price = Column(DECIMAL(20, 3), comment='商品价格')
	goods_id = Column(String(55), nullable=False, comment='商品ID')
	spec_type = Column(String(55), comment='商品规格')
	goods_count = Column(Integer, comment='件数')
	goods_code = Column(String(255), comment='商品编码')
	sample_status = Column(VARCHAR(55), comment='样品状态')
	logistics_id = Column(String(55), comment='快递单号')
	receiver = Column(String(55), comment='收货人')
	phone = Column(String(55), comment='收货人手机号')
	address = Column(String(255), comment='收货人地址')
	media = Column(VARCHAR(55), comment='媒介')
	create_name = Column(String(55), comment='创建人')
	sample_type = Column(String(55), comment='寄样类型')
	platform = Column(String(55), nullable=False, comment='社媒平台')

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):
		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)
		db.commit()

	@classmethod
	def insert(cls, db, data):
		item = cls(**data)
		db.add(item)

	@classmethod
	def update_data(cls, db,  start_date, end_date, platform, data):
		db.query(cls).filter(
			and_(
				cls.sample_time > start_date,
				cls.sample_time < end_date,
				cls.platform == platform
			)
		).update(data)


class BrandTmSycmStoreKey(BaseModel):

	__tablename__ = 'brand_tm_sycm_store_key'
	__table_args__ = {'comment': '进店搜索词'}

	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	search_key = Column(String(255), comment='搜索词')
	visitors_nums = Column(Integer, comment='访客数')
	view_nums = Column(Integer, comment='浏览量')
	buyer_nums = Column(Integer, comment='引到下单买家数')
	payment_conversion_rate = Column(DECIMAL(20, 3), comment='引到下单转换率')
	jump_rate = Column(DECIMAL(20, 3), comment='跳失率')
	trade_money = Column(Integer, comment='引导支付交易金额')
	payment_nums = Column(Integer, comment='引导支付件数')
	atv = Column(DECIMAL(20, 3), comment='客单价')
	uv_value = Column(DECIMAL(20, 3), comment='UV价值')

	@classmethod
	def update_data(cls, db, start_date, end_date, search_key, data):
		res = db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.search_key.in_(search_key)
			)
		).update(data)
		return res


class BrandTmSycmProfessionKey(BaseModel):
	__tablename__ = 'brand_tm_sycm_profession_key'
	__table_args__ = {'comment': '行业搜索词'}

	brand_tm_category_id = Column(Integer, nullable=False, comment='商品类目id')
	search_key = Column(VARCHAR(255), comment='搜索词')
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	hot_search_rank = Column(Integer, comment='热搜排名')
	search_nums = Column(Integer, comment='搜索人数')
	click_nums = Column(Integer, comment='点击人数')
	click_conversion_rate = Column(DECIMAL(20, 3), comment='点击率')
	payment_conversion_rate = Column(DECIMAL(20, 3), comment='支付转化率')
	subway_money = Column(DECIMAL(20, 3), comment='直通车参考价')
	shop_click_conversion_rate = Column(DECIMAL(20, 3), comment='商城点击占比')
	buyer_nums = Column(Integer, comment='买家数')

	@classmethod
	def update_data(cls, db, start_date, end_date, search_key,brand_tm_category_id, data):
		res = db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.search_key.in_(search_key),
				cls.brand_tm_category_id == brand_tm_category_id
			)
		).update(data)
		return res


class BrandTmSycmGoodsFlow(BaseModel):
	__tablename__ = 'brand_tm_sycm_goods_flow'
	__table_args__ = (
		Index('udx_sycm_goods_id_date_source', 'start_date', 'end_date', 'flow_source', 'goods_id', 'deleted_at', unique=True),
		{'comment': '生意参谋单品链接流量'}
	)
	start_date = Column(DateTime, nullable=False, comment='起始日期')
	end_date = Column(DateTime, nullable=False, comment='结束日期')
	flow_source = Column(VARCHAR(55), nullable=False, comment='流量来源')
	goods_id = Column(VARCHAR(255), nullable=False, comment='商品id')
	visitors_nums = Column(Integer, comment='访客数')
	buyer_nums = Column(Integer, comment='下单买家数')
	payment_conversion_rate = Column(DECIMAL(20, 3), comment='下单转换率')
	view_nums = Column(Integer, comment='浏览量')
	clt_cnt = Column(Integer, comment='收藏人数')
	add_cart_buyer = Column(Integer, comment='加购人数')
	pay_money = Column(DECIMAL(20, 3), comment='支付金额')
	pay_num = Column(Integer, comment='支付件数')
	pay_buyer = Column(Integer, comment='支付买家数')

	@classmethod
	def update_data(cls, db, start_date, end_date, goods_id, data):
		res = db.query(cls).filter(
			and_(
				cls.start_date == start_date,
				cls.end_date == end_date,
				cls.goods_id.in_(goods_id),
			)
		).update(data)
		return res


class BrandJdMonthTrade(BaseModel):
	__tablename__ = 'brand_jd_month_trade'
	__table_args__ = (
			Index('udx_webcrawler_brand_jd_month_trade_info_brand_id_name_month', 'brand_name', 'month', 'brand_id',
				  unique=True),
			{'comment': '京东各品牌月度交易数据\\r\\n'}
		)

	brand_name = Column(VARCHAR(55), nullable=False, comment='品牌名称')
	brand_id = Column(VARCHAR(55), nullable=False, comment='品牌id')
	month = Column(DateTime, nullable=False, comment='月份')
	trade_money = Column(DECIMAL(20, 3), nullable=False, comment='销售额')
	trade_count = Column(Integer, comment='销量')
	goods_average_price = Column(DECIMAL(10, 5), comment='宝贝均价')
	market_share = Column(DECIMAL(10, 5), comment='市场份额')
	goods_count = Column(Integer, comment='宝贝数')
	shop_count = Column(Integer, comment='店铺数')
	trade_money_change1 = Column(DECIMAL(10, 5), comment='销售额同比')
	trade_money_change2 = Column(DECIMAL(10, 5), comment='销售额环比')
	trade_count_change1 = Column(DECIMAL(10, 5), comment='销量同比')
	trade_count_change2 = Column(DECIMAL(10, 5), comment='销量环比')
	brand_channel_info_id = Column(Integer, nullable=False, comment='brand_channel_info表id')

	@classmethod
	def bulk_insert_on_duplicate(cls, db, data, update_keys):
		sql = """
			ALTER TABLE %s auto_increment=1;
					""" % (cls.__tablename__)
		# res = db.execute(sql)
		insert_stmt = insert(cls).values(data)
		update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
		update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
		on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
		db.execute(on_duplicate_key_stmt)
		db.execute(sql)
		db.commit()

	@classmethod
	def update_brand_channel_info_id(cls, db, month):
		sql = """
		UPDATE brand_jd_month_trade m
		JOIN (
			SELECT brand_id, brand_basic_info_id FROM brand_channel_info
			WHERE channel_str = '京东'
			) b 
		ON
		b.brand_id = m.brand_id
		SET
		m.brand_channel_info_id = b.brand_basic_info_id
		WHERE m.`month` = "%s";
		""" % month

		res = db.execute(sql)
		db.commit()

	@classmethod
	def get_brand_money(cls, db, month):
		res = db.query(cls.brand_name, cls.trade_money).filter(cls.month == month).all()
		return res

import base64
import json
import os
import random
import re
import time

import cv2
import requests
import urllib3
import numpy as np
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions \
    as Ec
from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.common.action_chains import ActionChains
from logx import logger
from settings import config

urllib3.disable_warnings()


class DyClientSpider:
    def __init__(self):
        chrome_options = webdriver.ChromeOptions()
        # 以下这些都是为了防止浏览器崩溃，而增加的一些属性
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # webdriver防检测
        # chrome_options.add_argument('--headless')  # # 浏览器不提供可视化页面
        chrome_options.add_argument('--disable-gpu')  # 禁用GPU加速,GPU加速可能会导致Chrome出现黑屏，且CPU占用率高达80%以上
        chrome_options.add_argument('--no-sandbox')  # 禁用沙盒，防止浏览器崩溃
        chrome_options.add_argument('--disable-dev-shm-usage')  # 不知道什么意思，也是防崩溃的
        # chrome_options.add_argument('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36')
        chrome_options.add_argument("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36",)

        self.browser = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.browser, 20)  # 设置等待时常，如果在指定时间内未获取到元素，则报错
        self.base_url = 'https://fxg.jinritemai.com'
        self.cookie = ""
        self.email = config.get("dy_account").get("email")
        self.password = config.get("dy_account").get("password")
        self.mobile = config.get("dy_account").get("mobile")

    def complete(self, time_out) -> bool:
        end_time = time.time() + time_out
        while True:
            if self.browser.current_url.startswith('https://fxg.jinritemai.com/ffa/mshop/homepage/index'):
                return True
            time.sleep(1)
            if time.time() > end_time:
                break
        return False

    def account_login(self) -> None:
        url = self.base_url + "/login"
        # 1. 请求网址
        self.browser.get(url)
        # 增加请求头，用户躲避滑动验证
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        random_sleep(3, 5)
        # expiry = self.browser.get_cookies()[0].get("expiry")
        self.browser.delete_all_cookies()
        self.browser.add_cookie(
            # 这里不能写
            {'domain': '.jinritemai.com', 'httpOnly': True, 'name': 'sso_auth_status', 'path': '/',
             'secure': True, 'value': '0b8e3a8f8871ceb6fb928a3994909832'})  # 必须首先加载网站，这样Selenium 才能知道cookie 属于哪个网站
        self.browser.get(url)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        # 2. 模拟登录
        # 切换登录模式为非扫码登录
        switch_mode_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-qrcode-switch-action'))
        )
        switch_mode_but.click()
        random_sleep()

        # 切换为邮箱登录
        switch_account_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.switch-switch'))
        )
        switch_account_but.click()
        random_sleep()

        # 输入邮箱
        email_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
        )

        email_input.send_keys(self.email)
        logger.info('邮箱已输入')
        random_sleep()

        # 输入密码
        password_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="password"]'))
        )
        password_input.send_keys(self.password)
        logger.info('密码已输入')
        random_sleep()

        # 点击登录
        submit_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-submit > .account-center-action-button'))
        )
        submit_but.click()

        # todo 这块用判断页面元素的方式不行，因为页面元素出来后，获取到的cookie还是不完整的
        # self.wait.until(
        #     Ec.presence_of_all_elements_located((By.CSS_SELECTOR, ".headerShopName"))
        # )
        # logger.info('登录成功')
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        # time.sleep(1)

        # 等待跳转到首页,10秒超时
        rt = 0
        if not self.complete(10):
            logger.info(f"current_url:{self.browser.current_url}")
            while True:
                rt += 1
                time.sleep(1)
                logger.info("login ct= {}".format(rt))
                if self.move():
                    break
        self.cookie = self.browser.get_cookies()
        self.save_cookie()

    def account_login2(self) -> None:
        url = self.base_url + "/login"
        # 1. 请求网址
        self.browser.get(url)
        # 增加请求头，用户躲避滑动验证
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        random_sleep(3, 5)
        # expiry = self.browser.get_cookies()[0].get("expiry")
        self.browser.delete_all_cookies()
        self.browser.add_cookie(
            # 这里不能写
            {'domain': '.jinritemai.com', 'httpOnly': True, 'name': 'sso_auth_status', 'path': '/',
             'secure': True, 'value': '0b8e3a8f8871ceb6fb928a3994909832'})  # 必须首先加载网站，这样Selenium 才能知道cookie 属于哪个网站
        self.browser.get(url)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        # 2. 模拟登录
        # 切换登录模式为非扫码登录
        switch_mode_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-qrcode-switch-action'))
        )
        switch_mode_but.click()
        random_sleep()

        # 切换为邮箱登录
        switch_account_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.switch-switch'))
        )
        switch_account_but.click()
        random_sleep()

        # 输入邮箱
        email_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="email"]'))
        )

        email_input.send_keys(self.email)
        logger.info('邮箱已输入')
        random_sleep()

        # 输入密码
        password_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="password"]'))
        )
        password_input.send_keys(self.password)
        logger.info('密码已输入')
        random_sleep()

        # 点击登录
        submit_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-submit > .account-center-action-button'))
        )
        submit_but.click()

        # todo 这块用判断页面元素的方式不行，因为页面元素出来后，获取到的cookie还是不完整的
        # self.wait.until(
        #     Ec.presence_of_all_elements_located((By.CSS_SELECTOR, ".headerShopName"))
        # )
        # logger.info('登录成功')
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        # time.sleep(1)

        # 等待跳转到首页,10秒超时
        rt = 0
        if not self.complete(10):
            logger.info(f"current_url:{self.browser.current_url}")

            # 发送验证码
            send_captcha = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-code-captcha'))
            )

            # send_captcha.click()
            logger.info('验证码已发送')

            captcha = input("输入验证码")

            # 输入验证码
            captcha_input = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="mobilecaptcha"]'))
            )
            captcha_input.send_keys(captcha)
            logger.info('密码已输入')
            random_sleep()

            # 点击登录
            submit_but = self.wait.until(
                Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-submit > .account-center-action-button'))
            )
            submit_but.click()
            random_sleep()
            print(self.browser.current_url)
            while True:
                rt += 1
                time.sleep(1)
                logger.info("login ct= {}".format(rt))
                if self.move():
                    break
        self.cookie = self.browser.get_cookies()
        self.save_cookie()

    def mobile_login(self) -> None:
        url = self.base_url + "/login"
        # 1. 请求网址
        self.browser.get(url)
        # 增加请求头，用户躲避滑动验证
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        random_sleep(3, 5)
        # expiry = self.browser.get_cookies()[0].get("expiry")
        self.browser.delete_all_cookies()
        self.browser.add_cookie(
            # 这里不能写
            {'domain': '.jinritemai.com', 'httpOnly': True, 'name': 'sso_auth_status', 'path': '/',
             'secure': True, 'value': '44b92c864bab79fec269c010e80667ce%2Ce3964c961f6e9b996771309389f5c2d1'})  # 必须首先加载网站，这样Selenium 才能知道cookie 属于哪个网站
        self.browser.get(url)
        js_str = "Object.defineProperty(navigator,'webdriver',{get: ()=> false,});"  # 在对象上定义一个新属性，或者修改这个属性的值
        self.browser.execute_script(js_str)
        logger.info('执行js')

        # 2. 模拟登录
        # 切换登录模式为非扫码登录
        switch_mode_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-qrcode-switch-action'))
        )
        switch_mode_but.click()
        random_sleep()

        # 输入手机号
        mobile_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="mobile"]'))
        )

        mobile_input.send_keys(self.mobile)
        logger.info('手机号已输入')
        random_sleep()

        # 发送验证码
        send_captcha = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-code-captcha'))
        )

        send_captcha.click()
        logger.info('验证码已发送')
        random_sleep()

        captcha = input("输入验证码")

        # 输入验证码
        captcha_input = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '[name="mobilecaptcha"]'))
        )
        captcha_input.send_keys(captcha)
        logger.info('密码已输入')
        random_sleep()

        # 点击登录
        submit_but = self.wait.until(
            Ec.element_to_be_clickable((By.CSS_SELECTOR, '.account-center-submit > .account-center-action-button'))
        )
        submit_but.click()

        # todo 这块用判断页面元素的方式不行，因为页面元素出来后，获取到的cookie还是不完整的
        # self.wait.until(
        #     Ec.presence_of_all_elements_located((By.CSS_SELECTOR, ".headerShopName"))
        # )
        # logger.info('登录成功')
        # todo 可能会出问题，如果页面没有完全加载，就无法获取到token或者只能获取到一部分token
        # time.sleep(1)

        # 等待跳转到首页,10秒超时
        rt = 0
        if not self.complete(10):
            logger.info(f"current_url:{self.browser.current_url}")
            while True:
                rt += 1
                time.sleep(1)
                logger.info("login ct= {}".format(rt))
                if self.move():
                    break
        self.cookie = self.browser.get_cookies()
        self.save_cookie()

    def save_cookie(self):
        cookie = {}
        for i in self.cookie:
            cookie[i.get("name")] = i.get("value")
        cookie_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        with open(cookie_path, mode="w+", encoding="utf-8") as fw:
            json.dump(cookie, fw)
            fw.flush()
            fw.close()

    def recognition(self):
        big_img = self.wait.until(
            Ec.element_to_be_clickable((By.ID, 'captcha-verify-image'))
        )
        big_src = big_img.get_attribute("src")
        small_img = self.wait.until(
            Ec.element_to_be_clickable((By.CLASS_NAME, 'react-draggable'))
        )
        small_src = small_img.get_attribute("src")

        big_req = requests.get(big_src)
        small_req = requests.get(small_src)
        captcha1_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "captcha1.png")
        captcha2_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "captcha2.png")
        r3_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "r3.jpg")
        r4_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "img", "r4.jpg")

        with open(captcha1_path, "wb") as f:
            f.write(big_req.content)
        with open(captcha2_path, "wb") as f:
            f.write(small_req.content)
        time.sleep(1)

        cv2.imwrite(r3_path, cv2.imread(captcha1_path, 0))
        cv2.imwrite(r4_path, cv2.imread(captcha2_path, 0))
        cv2.imwrite(r4_path, abs(255 - cv2.cvtColor(cv2.imread(r4_path), cv2.COLOR_BGR2GRAY)))
        result = cv2.matchTemplate(cv2.imread(r4_path), cv2.imread(r3_path), cv2.TM_CCOEFF_NORMED)
        x, y = np.unravel_index(result.argmax(), result.shape)

        cv2.rectangle(cv2.imread(r3_path), (y + 20, x + 20), (y + 136 - 25, x + 136 - 25), (7, 249, 151), 2)
        return y

    def move(self):
        xx = self.recognition()
        distance = int(xx / 1.625)
        # distance = int(xx * 281 / 360)  35# 计算缩放比(281网页中图片的宽度，360背景图的实际宽度)

        # 轨迹
        tracks = self._get_tracks(distance)

        # 移动滑块
        self._slider_action(tracks)

        if self.complete(15):
            return True

    def get_random_float(self,min, max, digits=4):
        """
        :param min:
        :param max:
        :param digits:
        :return:
        """
        return round(random.uniform(min, max), digits)

    # 滑动轨迹
    def _get_tracks(self, distance):

        track = []
        mid1 = round(distance * random.uniform(0.1, 0.2))
        mid2 = round(distance * random.uniform(0.65, 0.76))
        mid3 = round(distance * random.uniform(0.84, 0.88))
        # 设置初始位置、初始速度、时间间隔
        current, v, t = 0, 0, 0.2
        distance = round(distance)

        while current < distance:
            # 四段加速度
            if current < mid1:
                a = random.randint(10, 15)
            elif current < mid2:
                a = random.randint(30, 40)
            elif current < mid3:
                a = -70
            else:
                a = random.randint(-25, -18)

            # 初速度 v0
            v0 = v
            # 当前速度 v = v0 + at
            v = v0 + a * t
            v = v if v >= 0 else 0
            move = v0 * t + 1 / 2 * a * (t ** 2)
            move = round(move if move >= 0 else 1)
            # 当前位移
            current += move
            # 加入轨迹
            track.append(move)

        # 超出范围
        back_tracks = []
        out_range = distance - current
        if out_range < -8:
            sub = int(out_range + 8)
            back_tracks = [-1, sub, -3, -1, -1, -1, -1]
        elif out_range < -2:
            sub = int(out_range + 3)
            back_tracks = [-1, -1, sub]

        return {'forward_tracks': track, 'back_tracks': back_tracks}

    # 开始滑动
    def _slider_action(self, tracks):

        # 点击滑块
        small_img = self.wait.until(
            Ec.element_to_be_clickable((By.CLASS_NAME, 'react-draggable'))
        )
        ActionChains(self.browser).click_and_hold(on_element=small_img).perform()

        # 正向滑动
        for track in tracks['forward_tracks']:
            yoffset_random = random.uniform(-2, 4)
            ActionChains(self.browser).move_by_offset(xoffset=track, yoffset=yoffset_random).perform()

        time.sleep(random.uniform(0.06, 0.5))

        # 反向滑动
        for back_tracks in tracks['back_tracks']:
            yoffset_random = random.uniform(-2, 2)
            ActionChains(self.browser).move_by_offset(xoffset=back_tracks, yoffset=yoffset_random).perform()

        # 抖动
        ActionChains(self.browser).move_by_offset(
            xoffset=self.get_random_float(0, -1.67),
            yoffset=self.get_random_float(-1, 1)
        ).perform()
        ActionChains(self.browser).move_by_offset(
            xoffset=self.get_random_float(0, 1.67),
            yoffset=self.get_random_float(-1, 1)
        ).perform()

        time.sleep(self.get_random_float(0.6, 1))

        ActionChains(self.browser).release().perform()
        time.sleep(0.5)


def random_sleep(min_v=1, max_v=3) -> None:
    t = random.uniform(min_v, max_v)
    time.sleep(t)


if __name__ == '__main__':
    # DyClientSpider().account_login()
    # DyClientSpider().account_login2()
    DyClientSpider().mobile_login()

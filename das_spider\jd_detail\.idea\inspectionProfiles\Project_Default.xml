<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="8">
            <item index="0" class="java.lang.String" itemvalue="2.7" />
            <item index="1" class="java.lang.String" itemvalue="3.5" />
            <item index="2" class="java.lang.String" itemvalue="3.6" />
            <item index="3" class="java.lang.String" itemvalue="3.7" />
            <item index="4" class="java.lang.String" itemvalue="3.8" />
            <item index="5" class="java.lang.String" itemvalue="3.9" />
            <item index="6" class="java.lang.String" itemvalue="3.10" />
            <item index="7" class="java.lang.String" itemvalue="3.11" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="99">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="django" />
            <item index="2" class="java.lang.String" itemvalue="pymysql" />
            <item index="3" class="java.lang.String" itemvalue="Flask-Cache" />
            <item index="4" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="5" class="java.lang.String" itemvalue="django-redis" />
            <item index="6" class="java.lang.String" itemvalue="wordcloud" />
            <item index="7" class="java.lang.String" itemvalue="django-simple-captcha" />
            <item index="8" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="9" class="java.lang.String" itemvalue="scrapy" />
            <item index="10" class="java.lang.String" itemvalue="Flask" />
            <item index="11" class="java.lang.String" itemvalue="pycrypto" />
            <item index="12" class="java.lang.String" itemvalue="packaging" />
            <item index="13" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="14" class="java.lang.String" itemvalue="requests" />
            <item index="15" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="16" class="java.lang.String" itemvalue="Appium_Python_Client" />
            <item index="17" class="java.lang.String" itemvalue="selenium" />
            <item index="18" class="java.lang.String" itemvalue="lxml" />
            <item index="19" class="java.lang.String" itemvalue="nacos_sdk_python" />
            <item index="20" class="java.lang.String" itemvalue="Requests" />
            <item index="21" class="java.lang.String" itemvalue="redis" />
            <item index="22" class="java.lang.String" itemvalue="protobuf" />
            <item index="23" class="java.lang.String" itemvalue="bs4" />
            <item index="24" class="java.lang.String" itemvalue="greenlet" />
            <item index="25" class="java.lang.String" itemvalue="yarg" />
            <item index="26" class="java.lang.String" itemvalue="Brotli" />
            <item index="27" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="28" class="java.lang.String" itemvalue="h11" />
            <item index="29" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="30" class="java.lang.String" itemvalue="trio" />
            <item index="31" class="java.lang.String" itemvalue="appdirs" />
            <item index="32" class="java.lang.String" itemvalue="docopt" />
            <item index="33" class="java.lang.String" itemvalue="filelock" />
            <item index="34" class="java.lang.String" itemvalue="certifi" />
            <item index="35" class="java.lang.String" itemvalue="fake-useragent" />
            <item index="36" class="java.lang.String" itemvalue="soupsieve" />
            <item index="37" class="java.lang.String" itemvalue="PyExecJS" />
            <item index="38" class="java.lang.String" itemvalue="pyparsing" />
            <item index="39" class="java.lang.String" itemvalue="Appium-Python-Client" />
            <item index="40" class="java.lang.String" itemvalue="passlib" />
            <item index="41" class="java.lang.String" itemvalue="xlrd" />
            <item index="42" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="43" class="java.lang.String" itemvalue="wrapt" />
            <item index="44" class="java.lang.String" itemvalue="asgiref" />
            <item index="45" class="java.lang.String" itemvalue="cryptography" />
            <item index="46" class="java.lang.String" itemvalue="wsproto" />
            <item index="47" class="java.lang.String" itemvalue="attrs" />
            <item index="48" class="java.lang.String" itemvalue="urwid" />
            <item index="49" class="java.lang.String" itemvalue="cssselect" />
            <item index="50" class="java.lang.String" itemvalue="retrying" />
            <item index="51" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="52" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="53" class="java.lang.String" itemvalue="msgpack" />
            <item index="54" class="java.lang.String" itemvalue="idna" />
            <item index="55" class="java.lang.String" itemvalue="requests-html" />
            <item index="56" class="java.lang.String" itemvalue="rsa" />
            <item index="57" class="java.lang.String" itemvalue="jieba" />
            <item index="58" class="java.lang.String" itemvalue="async-timeout" />
            <item index="59" class="java.lang.String" itemvalue="trio-websocket" />
            <item index="60" class="java.lang.String" itemvalue="cffi" />
            <item index="61" class="java.lang.String" itemvalue="mitmproxy" />
            <item index="62" class="java.lang.String" itemvalue="h2" />
            <item index="63" class="java.lang.String" itemvalue="numpy" />
            <item index="64" class="java.lang.String" itemvalue="pyasn1" />
            <item index="65" class="java.lang.String" itemvalue="ldap3" />
            <item index="66" class="java.lang.String" itemvalue="w3lib" />
            <item index="67" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="68" class="java.lang.String" itemvalue="sniffio" />
            <item index="69" class="java.lang.String" itemvalue="Deprecated" />
            <item index="70" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="71" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="72" class="java.lang.String" itemvalue="zipp" />
            <item index="73" class="java.lang.String" itemvalue="tldextract" />
            <item index="74" class="java.lang.String" itemvalue="urllib3" />
            <item index="75" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="76" class="java.lang.String" itemvalue="pyee" />
            <item index="77" class="java.lang.String" itemvalue="outcome" />
            <item index="78" class="java.lang.String" itemvalue="websockets" />
            <item index="79" class="java.lang.String" itemvalue="blinker" />
            <item index="80" class="java.lang.String" itemvalue="kaitaistruct" />
            <item index="81" class="java.lang.String" itemvalue="hyperframe" />
            <item index="82" class="java.lang.String" itemvalue="pymongo" />
            <item index="83" class="java.lang.String" itemvalue="tornado" />
            <item index="84" class="java.lang.String" itemvalue="opencv-python" />
            <item index="85" class="java.lang.String" itemvalue="pyppeteer" />
            <item index="86" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="87" class="java.lang.String" itemvalue="pipreqs" />
            <item index="88" class="java.lang.String" itemvalue="publicsuffix2" />
            <item index="89" class="java.lang.String" itemvalue="parse" />
            <item index="90" class="java.lang.String" itemvalue="hpack" />
            <item index="91" class="java.lang.String" itemvalue="zstandard" />
            <item index="92" class="java.lang.String" itemvalue="chardet" />
            <item index="93" class="java.lang.String" itemvalue="tqdm" />
            <item index="94" class="java.lang.String" itemvalue="nacos-sdk-python" />
            <item index="95" class="java.lang.String" itemvalue="pytz" />
            <item index="96" class="java.lang.String" itemvalue="pyquery" />
            <item index="97" class="java.lang.String" itemvalue="openpyxl" />
            <item index="98" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="Lab.Ex2.Program.UIimport" />
          <option value="Lab.Lab06.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>
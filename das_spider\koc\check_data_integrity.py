import os
import re
import shutil


def delete_useless_file(filepath):
    for root, dir, files in os.walk(filepath):
        for file in files:
            res = re.findall('.*bb\d{2}', file)
            if res:
                pass
            else:
                print(file)
                os.remove(os.path.join(root, file))


def check_quantity(filepath):
    res = {}
    for root, dir, files in os.walk(filepath):
        page_list = []
        for file in files:
            if file.startswith("."):
                continue
            if file.endswith(os.path.basename(root)):
                page_param = file.split("%3f")[1].split("%26")[0]
                page_value = page_param.split("%3d")[1]
                page_list.append(int(page_value))
            else:
                print(file)

        if page_list:
            page_list.sort()
            # print(os.path.basename(root), page_list[-1], len(page_list))
            res[os.path.basename(root)] = [page_list[-1], len(page_list)]

    res = sorted(res.items(), key=lambda number: number[0])
    for k, v in res:
        if v[1]/v[0] > 0.99:
            reason = "通过"
        else:
            reason = "未通过"
        print("校验编号为",k, "应有数量: ",v[0], "实际数量: ",v[1], "完成度:", "{:.2%}".format(v[1]/v[0]), reason)

def split_file(filepath):
    for root, dirs, files in os.walk(filepath):
        for file in files:
            if file.startswith("."):
                continue
            file_postfix = file[-2:]
            file_dir = os.path.join(os.path.dirname(root), file_postfix)
            # print(file_dir)
            # print(os.path.join(file_dir, file))
            if not os.path.exists(file_dir):
                os.mkdir(file_dir)
            shutil.move(os.path.join(root,file), os.path.join(file_dir, file))


if __name__ == "__main__":
    # filepath = "userfile/user"
    # delete_useless_file(filepath)
    # split_file(filepath)
    check_quantity("userfile")
import time
from utils.logx import logger


def retry(func):
    """
    重试装饰器，当被装饰的函数，第一个返回值为false时，会重新调用该函数，重试超过3次会抛出异常
    :param func: 被装饰的函数
    :return: 被装饰函数的返回值
    """
    def wrapper(*args, **kwargs):
        retry_count = 0
        while retry_count < 3:
            retry_count += 1
            if retry_count > 1:
                logger.warning('retry method {} call count {}'.format(func.__name__, retry_count))
            res = func(*args, **kwargs)
            if res:
                if isinstance(res, list) and not res[0]:
                    continue
                return res
        logger.error('retry method {} call count {}'.format(func.__name__, retry_count))
        raise KeyboardInterrupt(f"{func.__name__}方法调用超出{retry_count}次")

    return wrapper

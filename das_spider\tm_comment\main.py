import datetime

from tm_comment_client import TmClientSpider


if __name__ == '__main__':
    now = datetime.datetime.now()
    # end_date = str(now - datetime.timedelta(days=1)).split()[0]
    end_date = str(now).split()[0]
    start_date = str(now - datetime.timedelta(days=7)).split()[0]
    # tm = TmClientSpider(start_date, end_date, "both")
    tm = TmClientSpider("2024-06-01", "2024-06-30", "both")
    tm.run()
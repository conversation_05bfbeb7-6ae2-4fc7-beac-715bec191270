import time


def format_timestamp(t) -> int:
    if not t:
        return 0
    if isinstance(t, str):
        if len(t) == 10:
            res = int(time.mktime(time.strptime(t, "%Y-%m-%d")))
        elif len(t) == 19:
            res = int(time.mktime(time.strptime(t, "%Y-%m-%d %H:%M:%S")))
        else:
            res = 0
        return res
    elif isinstance(t, int):
        return t
    return 0


def timestamp_str(t) -> str:
    if t < 1:
        return ''
    time_array = time.localtime(t)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_array)

"""
获取短信验证码
"""


import time
import datetime
import requests


def requests_code(channel):
    ret = {
        "err_code": 0,
        "err_msg": "",
        "data": {}
    }
    url = "http://gripper.aizinger.com:8889/api/jy/devices_message"
    params = {
        "channel": channel,
        "pageSize": 10,
        "pageNum": 1,
    }

    req = requests.request(method="GET", url=url, params=params)
    if req.status_code != 200 or not req.text:
        err_msg = "请求失败，原因未知"
        ret["err_code"] = -1
        ret["err_msg"] = err_msg
        return ret

    rsp = req.json()

    if rsp.get("code") != 0:
        ret["err_code"] = rsp.get("errCode")
        ret["err_msg"] = rsp.get("errMsg")
        return ret

    ret["data"] = rsp.get("data")
    return ret


def get_code(channel):
    ret = {
        "err_code": 0,
        "err_msg": "",
        "code": ""
    }
    for i in range(3):
        time.sleep(20)
        req = requests_code(channel)
        ret["err_code"] = req["err_code"]
        ret["err_msg"] = req["err_msg"]
        if req.get("err_code") != 0:
            break

        rows = req.get("data", [])
        for row in rows:
            if not row.get("send_time"):
                continue
            send_time = row.get("send_time").split(".")[0]
            code = row.get("code")
            sub_seconds = (
                    datetime.datetime.now() - datetime.datetime.strptime(send_time, "%Y-%m-%d %H:%M:%S")).seconds
            sub_minute = sub_seconds / 60
            if sub_minute <= 10:
                ret["code"] = code
                return ret
    return ret



[2025-07-08 10:05:41,323][MainThread:3984][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:05:44,593][MainThread:3984][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:14:19,978][MainThread:7432][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:14:22,876][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_main mode:month数据插入完成
[2025-07-08 10:15:06,443][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 10:15:08,660][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:15:10,693][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:15:12,651][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:15:14,585][MainThread:7432][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:17:38,914][MainThread:12696][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:18:37,866][MainThread:24184][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:21:11,504][MainThread:23032][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:21:13,455][MainThread:23032][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:21:14,069][MainThread:23032][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:21:58,024][MainThread:19180][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:22:00,676][MainThread:19180][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:22:03,314][MainThread:19180][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:22:45,171][MainThread:20860][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:22:47,996][MainThread:20860][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:22:50,218][MainThread:20860][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:23:15,630][MainThread:24984][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:23:18,271][MainThread:24984][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:23:20,502][MainThread:24984][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:23:47,591][MainThread:9192][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:23:50,256][MainThread:9192][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:23:51,566][MainThread:9192][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:24:02,006][MainThread:18060][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:24:04,741][MainThread:18060][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:24:06,795][MainThread:18060][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:30:57,375][MainThread:18652][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:31:26,754][MainThread:17480][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:31:29,476][MainThread:17480][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:31:34,705][MainThread:17480][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:31:55,586][MainThread:24712][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:31:58,627][MainThread:24712][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:32:02,077][MainThread:24712][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:34:30,008][MainThread:11648][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:34:32,666][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:34:35,308][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:37:29,952][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 10:38:14,704][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:38:47,698][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:39:35,064][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:39:47,068][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:357:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:50:54,731][MainThread:19636][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:50:57,583][MainThread:19636][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 10:51:01,931][MainThread:19636][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 10:58:51,370][MainThread:25708][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 10:59:05,475][MainThread:25708][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 10:59:16,990][MainThread:25708][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:59:28,098][MainThread:25708][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:59:38,915][MainThread:25708][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 10:59:41,064][MainThread:25708][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:00:00,428][MainThread:17676][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 11:00:03,348][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 11:00:04,359][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 11:00:27,423][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 11:00:38,549][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:00:49,996][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:01:01,314][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:01:03,396][MainThread:17676][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:05:05,756][MainThread:25396][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 11:05:07,557][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 11:05:08,680][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 11:05:38,072][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 11:05:54,938][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:06:11,820][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:06:27,903][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:06:30,727][MainThread:25396][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:06:43,823][MainThread:21300][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 11:06:45,533][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 11:06:46,717][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 11:07:00,120][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 11:07:11,530][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:07:23,181][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:07:34,958][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:07:37,632][MainThread:21300][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:08:31,939][MainThread:8224][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 11:08:33,688][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 11:08:34,668][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 11:09:55,025][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 11:10:27,078][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:11:00,356][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:11:32,149][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:11:37,102][MainThread:8224][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:12:07,256][MainThread:13232][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 11:12:09,530][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_main mode:month数据插入完成
[2025-07-08 11:13:05,531][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 11:13:21,200][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:13:35,905][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:13:50,706][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 11:13:53,331][MainThread:13232][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:34:29,279][MainThread:27040][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 13:34:31,061][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:34:32,008][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:34:57,417][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:35:20,930][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:35:44,494][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:36:07,986][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:36:15,214][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:36:15,294][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:36:19,808][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:37:33,376][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:37:56,437][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:38:19,426][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:38:43,232][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:38:47,176][MainThread:27040][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:40:51,647][MainThread:24692][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 13:40:54,421][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:40:55,809][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:41:16,147][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:41:31,234][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:41:46,862][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:42:01,559][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:42:04,426][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:42:04,565][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:42:05,944][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:43:09,024][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:43:32,816][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:43:57,055][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:44:20,305][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:44:24,249][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:44:24,350][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:44:25,271][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:44:41,970][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:44:56,187][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:45:11,619][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:45:26,236][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:45:28,810][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:45:28,921][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:45:29,851][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:46:51,070][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:47:34,345][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:48:18,133][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:49:01,624][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:49:08,579][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:49:08,690][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:49:09,640][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:50:29,240][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:51:11,192][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:51:53,069][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:52:35,392][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:52:42,146][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:52:42,236][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:TM, account_type:tm_test, mode:month完成
[2025-07-08 13:52:42,809][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:TM account_type:tm_test mode:month数据插入完成
[2025-07-08 13:53:41,932][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:35插入完成
[2025-07-08 13:54:03,374][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:54:25,391][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:54:46,943][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:54:50,525][MainThread:24692][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_tm_month_goods_trade_info表数据cid:********插入完成
[2025-07-08 13:57:28,869][MainThread:8112][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:00:47,382][MainThread:22116][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:01:30,427][MainThread:26620][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:04:36,187][MainThread:17396][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:06:41,559][MainThread:11432][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:20:55,650][MainThread:26344][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:20:57,367][MainThread:26344][task_id:INFO][INFO] [brand_qingbaotong_into.py:287:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:20:57,655][MainThread:26344][task_id:INFO][INFO] [brand_qingbaotong_into.py:215:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:21:05,900][MainThread:26344][task_id:INFO][INFO] [brand_qingbaotong_into.py:353:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:25:54,729][MainThread:3816][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:25:56,297][MainThread:3816][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:25:57,138][MainThread:3816][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:26:40,231][MainThread:3816][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:26:48,358][MainThread:3816][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:29:45,818][MainThread:5312][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:31:08,649][MainThread:5312][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_main mode:month数据插入完成
[2025-07-08 14:32:37,711][MainThread:5312][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:32:55,274][MainThread:5312][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:35:48,080][MainThread:3064][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:35:50,305][MainThread:3064][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_main mode:month数据插入完成
[2025-07-08 14:36:35,151][MainThread:3064][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:36:43,423][MainThread:3064][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:37:37,806][MainThread:27644][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:37:40,301][MainThread:27644][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_main mode:month数据插入完成
[2025-07-08 14:39:22,876][MainThread:27644][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:39:44,549][MainThread:27644][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:40:20,892][MainThread:22204][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:40:23,024][MainThread:22204][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_main mode:month数据插入完成
[2025-07-08 14:42:12,056][MainThread:22204][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:42:34,991][MainThread:22204][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:44:04,053][MainThread:11648][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:44:05,830][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:44:06,207][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:45:02,209][MainThread:11648][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:49:44,588][MainThread:8120][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:49:47,478][MainThread:8120][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:51:02,000][MainThread:8120][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:51:18,672][MainThread:8120][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:12218插入完成
[2025-07-08 14:52:09,056][MainThread:23900][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:52:10,851][MainThread:23900][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:52:11,218][MainThread:23900][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:52:20,967][MainThread:23900][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:12218插入完成
[2025-07-08 14:52:58,760][MainThread:14436][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:53:00,443][MainThread:14436][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:53:00,862][MainThread:14436][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:53:08,062][MainThread:14436][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:12218插入完成
[2025-07-08 14:53:11,726][MainThread:14436][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:53:31,319][MainThread:11624][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:53:33,033][MainThread:11624][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:53:33,447][MainThread:11624][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:53:49,368][MainThread:11624][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:12218插入完成
[2025-07-08 14:53:56,874][MainThread:11624][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:54:52,587][MainThread:25820][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:54:54,280][MainThread:25820][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:54:54,568][MainThread:25820][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:55:11,671][MainThread:25820][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:12218插入完成
[2025-07-08 14:55:20,204][MainThread:25820][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:55:23,253][MainThread:9388][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:55:25,012][MainThread:9388][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:55:25,275][MainThread:9388][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:55:38,375][MainThread:9388][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:55:50,684][MainThread:12236][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:55:52,502][MainThread:12236][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:55:52,906][MainThread:12236][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:56:00,463][MainThread:12236][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:56:12,888][MainThread:12236][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:56:34,250][MainThread:1564][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:56:36,017][MainThread:1564][task_id:INFO][INFO] [brand_qingbaotong_into.py:288:auto_add_brand] auto_add_brand  channel:JD, account_type:jd_test, mode:month完成
[2025-07-08 14:56:36,441][MainThread:1564][task_id:INFO][INFO] [brand_qingbaotong_into.py:216:dispose_brand_data] dispose_brand_data channel:JD account_type:jd_test mode:month数据插入完成
[2025-07-08 14:56:43,972][MainThread:1564][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:1319插入完成
[2025-07-08 14:56:56,576][MainThread:1564][task_id:INFO][INFO] [brand_qingbaotong_into.py:354:dispose_goods_data] brand_jd_month_goods_trade_info表数据cid:36720插入完成
[2025-07-08 14:58:29,170][MainThread:25224][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 14:59:20,305][MainThread:4176][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:03:54,521][MainThread:22560][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:06:03,664][MainThread:6216][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:09:07,806][MainThread:8268][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:09:07,888][MainThread:8268][task_id:INFO][INFO] [brand_goods.py:218:manual_match_spu] BrandGoods manual_match_spu start
[2025-07-08 15:09:17,365][MainThread:8268][task_id:INFO][INFO] [brand_goods.py:301:manual_match_spu] BrandGoods manual_match_spu end
[2025-07-08 15:18:50,669][MainThread:24968][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:20:05,698][MainThread:24968][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2024-08-01 - 2024-08-31
[2025-07-08 15:20:05,960][MainThread:24968][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:20:42,052][MainThread:24968][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:21:34,689][MainThread:24968][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2024-08-01 - 2024-08-31
[2025-07-08 15:24:06,106][MainThread:20960][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:24:15,030][MainThread:20960][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2024-09-01 - 2024-09-30
[2025-07-08 15:24:15,285][MainThread:20960][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:24:18,629][MainThread:20960][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:25:01,586][MainThread:20960][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2024-09-01 - 2024-09-30
[2025-07-08 15:25:41,940][MainThread:22572][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:25:46,576][MainThread:22572][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2024-10-01 - 2024-10-31
[2025-07-08 15:25:46,829][MainThread:22572][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:25:50,342][MainThread:22572][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:26:58,468][MainThread:22572][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2024-10-01 - 2024-10-31
[2025-07-08 15:28:22,924][MainThread:24420][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:28:24,270][MainThread:24420][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2024-11-01 - 2024-11-30
[2025-07-08 15:28:24,456][MainThread:24420][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:28:33,761][MainThread:24420][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:29:49,192][MainThread:24420][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2024-11-01 - 2024-11-30
[2025-07-08 15:30:05,425][MainThread:24472][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:30:06,714][MainThread:24472][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2024-12-01 - 2024-12-31
[2025-07-08 15:30:06,895][MainThread:24472][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:30:10,460][MainThread:24472][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:30:46,943][MainThread:24472][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2024-12-01 - 2024-12-31
[2025-07-08 15:31:41,149][MainThread:11948][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:31:42,528][MainThread:11948][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-01-01 - 2025-01-31
[2025-07-08 15:31:42,685][MainThread:11948][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:31:45,021][MainThread:11948][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:32:02,774][MainThread:1376][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:32:02,861][MainThread:1376][task_id:INFO][INFO] [brand_goods.py:218:manual_match_spu] BrandGoods manual_match_spu start
[2025-07-08 15:32:27,961][MainThread:11948][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-01-01 - 2025-01-31
[2025-07-08 15:32:43,049][MainThread:1376][task_id:INFO][INFO] [brand_goods.py:301:manual_match_spu] BrandGoods manual_match_spu end
[2025-07-08 15:32:46,645][MainThread:25848][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:32:47,954][MainThread:25848][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-02-01 - 2025-02-28
[2025-07-08 15:32:48,149][MainThread:25848][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:32:50,969][MainThread:25848][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:33:29,348][MainThread:25848][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-02-01 - 2025-02-28
[2025-07-08 15:33:35,127][MainThread:22732][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:33:36,496][MainThread:22732][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-03-01 - 2025-03-31
[2025-07-08 15:33:36,627][MainThread:22732][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:33:41,930][MainThread:22732][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:34:24,044][MainThread:22732][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-03-01 - 2025-03-31
[2025-07-08 15:34:32,945][MainThread:25124][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:34:34,266][MainThread:25124][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-04-01 - 2025-04-30
[2025-07-08 15:34:35,499][MainThread:25124][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:34:38,366][MainThread:25124][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:35:11,571][MainThread:25124][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-04-01 - 2025-04-30
[2025-07-08 15:37:35,043][MainThread:1376][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:37:36,352][MainThread:1376][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-05-01 - 2025-05-31
[2025-07-08 15:37:36,487][MainThread:1376][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:37:38,640][MainThread:1376][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:38:14,536][MainThread:1376][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-05-01 - 2025-05-31
[2025-07-08 15:38:26,002][MainThread:26992][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:38:27,309][MainThread:26992][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['trade', 'goods'], 2025-06-01 - 2025-06-30
[2025-07-08 15:38:27,585][MainThread:26992][task_id:INFO][INFO] [brand_handle.py:116:get_dy_chanmama_trade] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:38:29,995][MainThread:26992][task_id:INFO][INFO] [brand_handle.py:194:get_dy_chanmama_goods] BrandDyData get BrandChannelInfo. channel is DY, platform is chanmama, res is [简爱, LEPUR/乐纯, CLASSY.KISS/卡士]
[2025-07-08 15:39:08,829][MainThread:26992][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['trade', 'goods'], data: 2025-06-01 - 2025-06-30
[2025-07-08 15:40:31,439][MainThread:21616][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:40:35,473][MainThread:21616][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2024-08-01 - 2024-08-31
[2025-07-08 15:42:08,958][MainThread:26844][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:43:52,971][MainThread:23340][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:44:56,411][MainThread:776][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 15:45:09,902][MainThread:27040][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:37:24,565][MainThread:28104][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:39:21,966][MainThread:28104][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-06-01 - 2025-06-30
[2025-07-08 16:43:58,339][MainThread:28104][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-06-01 - 2025-06-30
[2025-07-08 16:44:17,094][MainThread:28636][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:44:20,402][MainThread:28636][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-07-01 - 2025-07-31
[2025-07-08 16:44:24,730][MainThread:28636][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-07-01 - 2025-07-31
[2025-07-08 16:44:36,693][MainThread:16656][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:44:39,576][MainThread:16656][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-06-01 - 2025-06-30
[2025-07-08 16:44:42,764][MainThread:16656][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-06-01 - 2025-06-30
[2025-07-08 16:44:52,663][MainThread:23288][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:44:55,800][MainThread:23288][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-05-01 - 2025-05-31
[2025-07-08 16:45:25,648][MainThread:23288][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-05-01 - 2025-05-31
[2025-07-08 16:45:31,867][MainThread:27224][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:45:34,300][MainThread:27224][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-04-01 - 2025-04-30
[2025-07-08 16:47:18,320][MainThread:27224][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-04-01 - 2025-04-30
[2025-07-08 16:47:28,509][MainThread:27516][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:47:34,930][MainThread:27516][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-03-01 - 2025-03-31
[2025-07-08 16:48:23,171][MainThread:27516][task_id:INFO][INFO] [brand_handle.py:738:run] 竞对抖音蝉妈妈数据 获取成功. 参数为: task_list ['refresh_goods_id'], data: 2025-03-01 - 2025-03-31
[2025-07-08 16:48:53,397][MainThread:14040][task_id:INFO][INFO] [logx.py:143:load_my_logging_cfg] It works!
[2025-07-08 16:49:00,207][MainThread:14040][task_id:INFO][INFO] [brand_handle.py:722:run] BrandDyData run. params is: ['refresh_goods_id'], 2025-02-01 - 2025-02-28

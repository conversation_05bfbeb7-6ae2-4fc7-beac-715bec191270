# coding: utf-8
import time
from sqlalchemy import Column, Float, DateTime, String, event, Index, text
from sqlalchemy.dialects.mysql import INTEGER, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class TbSycmCompetesSell(Base):
    __tablename__ = 'tb_sycm_competes_sell'
    __table_args__ = (
        Index('ind_tb_sycm_competes_sell_shop_id_date', 'shop_id', 'date'),
    )

    id = Column(INTEGER(11), primary_key=True)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    deleted_at = Column(DateTime)
    remark = Column(String(255))
    shop_name = Column(VARCHAR(55), comment='店铺名称')
    shop_id = Column(VARCHAR(55), comment='店铺ID')
    type = Column(VARCHAR(55), comment='类别')
    date = Column(VARCHAR(55), comment='日期')
    category_name = Column(VARCHAR(255), comment='类目名称')
    trade_money = Column(Float, comment='交易金额')
    visitors_nums = Column(INTEGER(11), comment='访客人数')
    search_nums = Column(INTEGER(11), comment='搜索人数')
    collect_nums = Column(INTEGER(11), comment='收藏人数')
    add_shop_nums = Column(INTEGER(11), comment='加购人数')
    payment_conversion_rate = Column(Float, comment='支付转化率')
    buyer_nums = Column(INTEGER(11), comment='买家数')
    atv = Column(Float, comment='客单价')
    uv_value = Column(Float, comment='UV价值')
    search_rate = Column(Float, comment='搜索占比')
    collect_rate = Column(Float, comment='收藏率')
    add_shop_rate = Column(Float, comment='加购率')
    collect_add_shop_rate = Column(Float, comment='收藏加购率')
    pre_trade_money = Column(INTEGER(11), comment='预售定金交易金额')
    pre_pay_goods_count = Column(INTEGER(11), comment='预售支付商品件数')
    update_goods_count = Column(INTEGER(11), comment='上新商品数')

    @staticmethod
    def insert_if_null(session):
        sql = """
        INSERT INTO tb_sycm_competes_sell SELECT
        t.* 
        FROM
            tb_sycm_competes_sell sell
            RIGHT JOIN tb_sycm_competes_sell_tmp t ON sell.shop_id = t.shop_id 
            AND sell.date = t.date 
        WHERE
            sell.id IS NULL;
        """
        session.execute(sql)

    @staticmethod
    def update_if_exists(session):
        sql = """
        update tb_sycm_competes_sell sell
        join tb_sycm_competes_sell_tmp t
        on sell.shop_id = t.shop_id
        and sell.date = t.date
        set sell.updated_at = NOW(), sell.trade_money = t.trade_money, sell.visitors_nums = t.visitors_nums,
        sell.search_nums = t.search_nums, sell.collect_nums = t.collect_nums, sell.add_shop_nums = t.add_shop_nums,
        sell.payment_conversion_rate = t.payment_conversion_rate, sell.buyer_nums = t.buyer_nums,
        sell.atv = t.atv, sell.uv_value = t.uv_value, sell.search_rate = t.search_rate,
        sell.collect_rate = t.collect_rate, sell.add_shop_rate = t.add_shop_rate,
        sell.collect_add_shop_rate = t.collect_add_shop_rate, sell.pre_trade_money = t.pre_trade_money,
        sell.pre_pay_goods_count = t.pre_pay_goods_count, sell.update_goods_count = t.update_goods_count;
        """
        session.execute(sql)

    @staticmethod
    def truncate_table(session):
        sql = """
        TRUNCATE TABLE tb_sycm_competes_sell_tmp;
        """
        session.execute(sql)


class TbSycmCompetesCustomer(Base):
    __tablename__ = 'tb_sycm_competes_customer'
    __table_args__ = (
        Index('ind_tb_sycm_competes_customer_unique_id', 'shop_id', 'date', 'customer_source'),
    )

    id = Column(INTEGER(11), primary_key=True)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    deleted_at = Column(DateTime)
    remark = Column(VARCHAR(255))
    shop_name = Column(VARCHAR(55), comment='店铺名称')
    shop_id = Column(VARCHAR(55), comment='店铺ID')
    type = Column(VARCHAR(55), comment='类别')
    date = Column(VARCHAR(55), comment='日期')
    customer_source = Column(VARCHAR(55), comment='流量来源')
    visitors_nums = Column(INTEGER(11), comment='访客人数')
    customer_nums = Column(INTEGER(11), comment='买家数')
    payment_conversion_rate = Column(Float, comment='支付转化率')
    trade_money = Column(Float, comment='交易金额')
    uv_value = Column(Float, comment='UV价值')
    atv = Column(Float, comment='客单价')

    @staticmethod
    def insert_if_null(session):
        sql = """
        INSERT INTO tb_sycm_competes_customer SELECT
        t.* 
        FROM
            tb_sycm_competes_customer cust
            RIGHT JOIN tb_sycm_competes_customer_tmp t ON cust.shop_id = t.shop_id 
            AND cust.date = t.date 
            AND cust.customer_source = t.customer_source 
        WHERE
            cust.id IS NULL;
        """
        session.execute(sql)

    @staticmethod
    def update_if_exists(session):
        sql = """
        update tb_sycm_competes_customer cust
        join tb_sycm_competes_customer_tmp t
        on cust.shop_id = t.shop_id
        and cust.date = t.date
        and cust.customer_source = t.customer_source
        set cust.updated_at = NOW(), cust.visitors_nums = t.visitors_nums, cust.customer_nums = t.customer_nums, 
        cust.payment_conversion_rate = t.payment_conversion_rate, cust.trade_money = t.trade_money, 
        cust.uv_value = t.uv_value, cust.atv = t.atv;
        """
        session.execute(sql)

    @staticmethod
    def truncate_table(session):
        sql = """
        TRUNCATE TABLE tb_sycm_competes_customer_tmp;
        """
        session.execute(sql)


class TbSycmCompetesGoods(Base):
    __tablename__ = 'tb_sycm_competes_goods'
    __table_args__ = (
        Index('ind_tb_sycm_competes_sell_shop_id_date', 'shop_id', 'date'),
    )

    id = Column(INTEGER(11), primary_key=True)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    deleted_at = Column(DateTime)
    remark = Column(String(255))
    shop_name = Column(VARCHAR(55), comment='店铺名称')
    shop_id = Column(VARCHAR(55), comment='店铺ID')
    type = Column(VARCHAR(55), comment='类别')
    date = Column(VARCHAR(55), comment='日期')
    goods_name = Column(String(255), comment='商品名称')
    goods_url = Column(String(555), comment='商品链接')
    goods_flow_index = Column(INTEGER(11), comment='流量指数')
    goods_flow_value = Column(INTEGER(11), comment='流量指数值')
    goods_flow_rate = Column(Float, comment='流量指数变化')


    @staticmethod
    def insert_if_null(session):
        sql = """
        INSERT INTO tb_sycm_competes_goods SELECT
        t.* 
        FROM
            tb_sycm_competes_goods goods
            RIGHT JOIN tb_sycm_competes_goods_tmp t ON goods.shop_id = t.shop_id 
            AND goods.date = t.date AND goods.goods_url = t.goods_url
        WHERE
            goods.id IS NULL;
        """
        session.execute(sql)

    @staticmethod
    def update_if_exists(session):
        # sql = """
        # """
        # session.execute(sql)
        pass

    @staticmethod
    def truncate_table(session):
        sql = """
        TRUNCATE TABLE tb_sycm_competes_goods_tmp;
        """
        session.execute(sql)
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="12d757b2-19e6-45c5-ad1c-c4c3e1ae6305" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/boss/work_desc/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/boss/work_desc/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/boss/work_desc/job_info.py" beforeDir="false" afterPath="$PROJECT_DIR$/boss/work_desc/job_info.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/boss/work_desc/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/boss/work_desc/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/brand_goods.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/brand_goods.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/brand_qingbaotong.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/brand_qingbaotong.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/brand_sku_goods.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/brand_sku_goods.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/brand_sycm_key_into.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/brand_sycm_key_into.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/chanmama/apis.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/chanmama/apis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/intelligence/apis.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/intelligence/apis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/intelligence/login.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/intelligence/login.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/intelligence/test_intelligence.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/intelligence/test_intelligence.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/moojing/apis.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/moojing/apis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/moojing/mojin_login.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/moojing/mojin_login.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/moojing_good_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/moojing_good_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/settings/情报通_商品过滤器.json" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/settings/情报通_商品过滤器.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/brand_data/week_main.py" beforeDir="false" afterPath="$PROJECT_DIR$/brand_data/week_main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jd_comment/comment.py" beforeDir="false" afterPath="$PROJECT_DIR$/jd_comment/comment.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/price_cat/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/price_cat/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/price_cat/price_cat/settings.py" beforeDir="false" afterPath="$PROJECT_DIR$/price_cat/price_cat/settings.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/brand_goods_monitor.py" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/brand_goods_monitor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/cy_video.py" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/cy_video.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/download_video.py" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/download_video.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/qc_customer_record.py" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/qc_customer_record.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/qc_expense_reduce_record.py" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/qc_expense_reduce_record.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shadow_data/settings/dev.json" beforeDir="false" afterPath="$PROJECT_DIR$/shadow_data/settings/dev.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sycm_data/sycm_comparison_goods.py" beforeDir="false" afterPath="$PROJECT_DIR$/sycm_data/sycm_comparison_goods.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sycm_data/sycm_goods_source.py" beforeDir="false" afterPath="$PROJECT_DIR$/sycm_data/sycm_goods_source.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xhs_data/database/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/xhs_data/database/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xhs_data/settings/dev.json" beforeDir="false" afterPath="$PROJECT_DIR$/xhs_data/settings/dev.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xhs_data/xhs_key_search.py" beforeDir="false" afterPath="$PROJECT_DIR$/xhs_data/xhs_key_search.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xhs_data/xhs_put_cost.py" beforeDir="false" afterPath="$PROJECT_DIR$/xhs_data/xhs_put_cost.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/message_auth" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/brand_data" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1720598169" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1720598139" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/message_auth" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1723796187" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1723796181" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/price_cat" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1740388072" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1740388027" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1741243976" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1741243968" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/voc_yuyi" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat_wzz" />
                    <option name="lastUsedInstant" value="1743477573" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1743477393" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feat-lj" />
                    <option name="lastUsedInstant" value="1720752653" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/brand_data" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1744256378" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1744256370" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/ali_code_review" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1745822445" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1745822445" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/sycm_data" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1746519491" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1746519478" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/shadow_data" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1750733651" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1750733627" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/xhs_data" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1750910603" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feat-wzz" />
                    <option name="lastUsedInstant" value="1749455398" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/wdt_data" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/xhs_data/main.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
    <option name="sortByName" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2PLyOrfbJgqfUr6RgJTjgtuM6hL" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Node.js.11.js.executor": "Run",
    "Node.js.111.js.executor": "Run",
    "Node.js.app.bfcb36b5.js.executor": "Run",
    "Node.js.app.js.executor": "Run",
    "Node.js.chanmama_decrypt.js.executor": "Run",
    "Node.js.chunk-vendors.1f634552.js.executor": "Run",
    "Node.js.cy.js (1).executor": "Run",
    "Node.js.cy.js.executor": "Run",
    "Node.js.demo.js.executor": "Run",
    "Node.js.signer.js.executor": "Run",
    "Node.js.tiktokSigner.mjs.executor": "Run",
    "Node.js.utils.js.executor": "Run",
    "Node.js.webmssdk.js.executor": "Run",
    "Python 测试.Python tests for test_chanmama.TestCases.test.executor": "Debug",
    "Python 测试.Python tests for test_chanmama.TestCases.test_get_product_info.executor": "Debug",
    "Python 测试.Python tests for test_chanmama.TestCases.test_get_v1_brand_detail_card_list.executor": "Debug",
    "Python 测试.Python tests for test_intelligence.TestCases.test_get_hot_brand_info.executor": "Run",
    "Python 测试.Python tests for test_intelligence.TestCases.test_get_hot_goods_info.executor": "Debug",
    "Python 测试.Python tests for test_moojing.TestCases.test_get_detail_comment.executor": "Debug",
    "Python 测试.Python tests for test_moojing.TestCases.test_get_moojing_trade_data.executor": "Debug",
    "Python 测试.Python tests for test_voc_new.TestCases.test_get_dialog_search.executor": "Debug",
    "Python 测试.Python tests for test_voc_new.TestCases.test_get_v1_dialog.executor": "Debug",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_fans_data.executor": "Run",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_goods_note_detail.executor": "Debug",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_keyword_recommend.executor": "Debug",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_keyword_search.executor": "Debug",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_put_cost.executor": "Debug",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_seller_carrier_item.executor": "Run",
    "Python 测试.Python tests for test_xhs.TestCases.test_get_taxonomy.executor": "Run",
    "Python 测试.Python tests for test_xhs.TestCases.test_grass_articles.executor": "Debug",
    "Python 测试.test_chanmama.TestCases.test_get_v1_brand_detail_card_list 的 Python 测试.executor": "Run",
    "Python 测试.test_chanmama.TestCases.test_get_v2_product_live_room 的 Python 测试.executor": "Run",
    "Python 测试.test_chanmama.TestCases.test_get_v6_brand_detail_basic 的 Python 测试.executor": "Run",
    "Python 测试.test_chanmama.TestCases.test_get_v6_home_sale_rank 的 Python 测试.executor": "Debug",
    "Python 测试.test_intelligence.TestCases.test_get_hot_brand_info 的 Python 测试.executor": "Run",
    "Python 测试.test_intelligence.TestCases.test_get_hot_goods_info 的 Python 测试.executor": "Debug",
    "Python 测试.test_moojing.TestCases.test_get_detail_comment 的 Python 测试.executor": "Debug",
    "Python 测试.test_moojing.TestCases.test_get_list_item_id 的 Python 测试.executor": "Run",
    "Python 测试.test_moojing.TestCases.test_get_moojing_good_data 的 Python 测试.executor": "Run",
    "Python 测试.test_moojing.TestCases.test_get_moojing_trade_data 的 Python 测试.executor": "Run",
    "Python 测试.test_moojing.TestCases.test_get_uid 的 Python 测试.executor": "Debug",
    "Python 测试.test_qc.TestCases.test_grid_data 的 Python 测试.executor": "Debug",
    "Python 测试.test_shadow.TestCases.test_grid_data 的 Python 测试.executor": "Run",
    "Python 测试.test_shadow.TestCases.test_query_record_list 的 Python 测试.executor": "Run",
    "Python 测试.test_xhs.TestCases.test_get_put_cost 的 Python 测试.executor": "Run",
    "Python 测试.test_xhs.TestCases.test_get_search_word_daily_data 的 Python 测试.executor": "Run",
    "Python.1.executor": "Run",
    "Python.111.executor": "Run",
    "Python.11111.executor": "Run",
    "Python.__init__.executor": "Run",
    "Python.activity_check.executor": "Debug",
    "Python.address_match.executor": "Run",
    "Python.ali_bill_details_put.executor": "Debug",
    "Python.apis (1).executor": "Run",
    "Python.apis.executor": "Debug",
    "Python.brand_chanqq_info.executor": "Run",
    "Python.brand_goods.executor": "Run",
    "Python.brand_goods_check (1).executor": "Run",
    "Python.brand_goods_check.executor": "Run",
    "Python.brand_goods_into.executor": "Run",
    "Python.brand_goods_monitor.executor": "Run",
    "Python.brand_handle.executor": "Debug",
    "Python.brand_qingbaotong (1).executor": "Debug",
    "Python.brand_qingbaotong.executor": "Run",
    "Python.brand_qingbaotong_info.executor": "Run",
    "Python.brand_qingbaotong_into.executor": "Run",
    "Python.brand_sku_goods.executor": "Run",
    "Python.brand_sycm_key_into.executor": "Run",
    "Python.comment.executor": "Run",
    "Python.comment_handle.executor": "Debug",
    "Python.comment_handle_bak.executor": "Debug",
    "Python.comment_handle_bak01.executor": "Debug",
    "Python.cw_put_data.executor": "Run",
    "Python.cy_demo.executor": "Run",
    "Python.cy_video.executor": "Run",
    "Python.demo (1).executor": "Run",
    "Python.demo.executor": "Run",
    "Python.demo1.executor": "Debug",
    "Python.demo_qq.executor": "Run",
    "Python.demoqq.executor": "Run",
    "Python.dingtalk.executor": "Run",
    "Python.download_video.executor": "Run",
    "Python.dy_shop_goods_put.executor": "Run",
    "Python.dy_video.executor": "Debug",
    "Python.encryption.executor": "Run",
    "Python.inventory_details_put.executor": "Run",
    "Python.login.executor": "Debug",
    "Python.main (1).executor": "Run",
    "Python.main.executor": "Debug",
    "Python.main_deail_put_into.executor": "Run",
    "Python.main_moojing.executor": "Debug",
    "Python.main_moojing_put_into.executor": "Run",
    "Python.main_new.executor": "Run",
    "Python.main_xhs_grass_articles.executor": "Run",
    "Python.mojin_login.executor": "Run",
    "Python.monitor_status.executor": "Debug",
    "Python.moojing_good_data.executor": "Debug",
    "Python.new_voc_comment.executor": "Run",
    "Python.new_voc_dialog.executor": "Debug",
    "Python.qc_apis.executor": "Run",
    "Python.qc_customer_record.executor": "Run",
    "Python.qc_data.executor": "Debug",
    "Python.qc_expense_reduce_record.executor": "Run",
    "Python.shadow_query_record.executor": "Run",
    "Python.sycm_comparison_goods.executor": "Run",
    "Python.sycm_competition_loss.executor": "Run",
    "Python.sycm_goods_optimize_key.executor": "Run",
    "Python.sycm_goods_rank.executor": "Run",
    "Python.sycm_goods_source.executor": "Run",
    "Python.tm_comment_put.executor": "Run",
    "Python.tm_login.executor": "Debug",
    "Python.tm_shop_goods_put.executor": "Debug",
    "Python.week_main.executor": "Debug",
    "Python.xhs_fans_data.executor": "Run",
    "Python.xhs_goods_detail.executor": "Debug",
    "Python.xhs_goods_note_detail.executor": "Debug",
    "Python.xhs_grass_articles.executor": "Run",
    "Python.xhs_key_search.executor": "Debug",
    "Python.xhs_put_cost.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "feat__wzz",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "E:/message_auth/das_spider/sycm_data/settings",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "ts.external.directory.path": "D:\\software\\PyCharm 2024.1.6\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\message_auth\das_spider\sycm_data\settings" />
      <recent name="E:\message_auth\das_spider\brand_data\settings" />
      <recent name="E:\message_auth\das_spider\jd_comment\settings" />
      <recent name="E:\message_auth\das_spider\shadow_data" />
      <recent name="E:\message_auth\das_spider\shadow_data\qc_api" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\message_auth\das_spider\xhs_data\xhs_apis" />
      <recent name="E:\message_auth\das_spider\shadow_data" />
      <recent name="E:\message_auth\das_spider\sycm_data\settings" />
      <recent name="E:\message_auth\das_spider\xhs_data" />
      <recent name="E:\message_auth\das_spider\brand_data" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <myKeys>
      <visibility group="Grunt" flag="true" />
      <visibility group="Gulp" flag="true" />
      <visibility group="HTTP 请求" flag="true" />
      <visibility group="Node.js" flag="true" />
      <visibility group="Run Python file" flag="true" />
      <visibility group="Run conda command" flag="true" />
      <visibility group="Run pip command" flag="true" />
      <visibility group="npm" flag="true" />
      <visibility group="yarn" flag="true" />
      <visibility group="最近的项目" flag="true" />
      <visibility group="运行 Python 文件" flag="true" />
      <visibility group="运行 conda 命令" flag="true" />
      <visibility group="运行 pip 命令" flag="true" />
      <visibility group="运行配置" flag="true" />
    </myKeys>
  </component>
  <component name="RunManager" selected="Python.brand_goods">
    <configuration name="brand_goods" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="das_spider" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/brand_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/brand_data/brand_goods.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="brand_qingbaotong" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="das_spider" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/brand_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/brand_data/brand_qingbaotong.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="brand_qingbaotong_into" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="das_spider" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/brand_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="das_spider" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/brand_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/brand_data/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="sycm_comparison_goods" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="das_spider" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/sycm_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/sycm_data/sycm_comparison_goods.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.main" />
      <item itemvalue="Python.brand_goods" />
      <item itemvalue="Python.brand_qingbaotong" />
      <item itemvalue="Python.brand_qingbaotong_into" />
      <item itemvalue="Python.sycm_comparison_goods" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.brand_goods" />
        <item itemvalue="Python.brand_qingbaotong_into" />
        <item itemvalue="Python.brand_qingbaotong" />
        <item itemvalue="Python.sycm_comparison_goods" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19072.16" />
        <option value="bundled-python-sdk-8336bb23522e-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19072.16" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="12d757b2-19e6-45c5-ad1c-c4c3e1ae6305" name="更改" comment="" />
      <created>1683250628492</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1683250628492</updated>
      <workItem from="1683250629504" duration="7336000" />
      <workItem from="1683259058279" duration="15915000" />
      <workItem from="1683278618962" duration="1446000" />
      <workItem from="1683281748124" duration="804000" />
      <workItem from="1683337492653" duration="4694000" />
      <workItem from="1683349655522" duration="25965000" />
      <workItem from="1683611998045" duration="1142000" />
      <workItem from="1683770835247" duration="1915000" />
      <workItem from="1683774404292" duration="4090000" />
      <workItem from="1683859703677" duration="422000" />
      <workItem from="1683860196319" duration="15029000" />
      <workItem from="1684467018192" duration="1769000" />
      <workItem from="1684741225159" duration="7000" />
      <workItem from="1684809132529" duration="13495000" />
      <workItem from="1684891409655" duration="23757000" />
      <workItem from="1685080199914" duration="3717000" />
      <workItem from="1685411893837" duration="6872000" />
      <workItem from="1685496559523" duration="5244000" />
      <workItem from="1685519850216" duration="63000" />
      <workItem from="1685519927988" duration="2004000" />
      <workItem from="1685757316678" duration="599000" />
      <workItem from="1685758236373" duration="5842000" />
      <workItem from="1686131862353" duration="645000" />
      <workItem from="1686812863261" duration="656000" />
      <workItem from="1686902342168" duration="686000" />
      <workItem from="1687224897195" duration="3797000" />
      <workItem from="1687243516936" duration="778000" />
      <workItem from="1687244353135" duration="672000" />
      <workItem from="1687417850729" duration="1380000" />
      <workItem from="1687685363648" duration="1480000" />
      <workItem from="1687745285557" duration="686000" />
      <workItem from="1687764745551" duration="5499000" />
      <workItem from="1687854489944" duration="1240000" />
      <workItem from="1688007319353" duration="1008000" />
      <workItem from="1688043122927" duration="1104000" />
      <workItem from="1688193611766" duration="3087000" />
      <workItem from="1688359083084" duration="72838000" />
      <workItem from="1688628368098" duration="39776000" />
      <workItem from="1688957917548" duration="31439000" />
      <workItem from="1689232452044" duration="20596000" />
      <workItem from="1689326660112" duration="3436000" />
      <workItem from="1689384291333" duration="9780000" />
      <workItem from="1689406534078" duration="12359000" />
      <workItem from="1689837297834" duration="6723000" />
      <workItem from="1690187442153" duration="1922000" />
      <workItem from="1690365330949" duration="1413000" />
      <workItem from="1690422099173" duration="2991000" />
      <workItem from="1690438374136" duration="7977000" />
      <workItem from="1690766987451" duration="11921000" />
      <workItem from="1690867391579" duration="10920000" />
      <workItem from="1690943218678" duration="4628000" />
      <workItem from="1690955058814" duration="41152000" />
      <workItem from="1691116513661" duration="16993000" />
      <workItem from="1691372356729" duration="819000" />
      <workItem from="1691378283131" duration="4902000" />
      <workItem from="1691399698903" duration="209000" />
      <workItem from="1691399918978" duration="30976000" />
      <workItem from="1691488818209" duration="4820000" />
      <workItem from="1691544327514" duration="66625000" />
      <workItem from="1691732539468" duration="10849000" />
      <workItem from="1691978394855" duration="26164000" />
      <workItem from="1692149976569" duration="21960000" />
      <workItem from="1692264638920" duration="5295000" />
      <workItem from="1692581288161" duration="3317000" />
      <workItem from="1692602310428" duration="11130000" />
      <workItem from="1692699255947" duration="13965000" />
      <workItem from="1692841152926" duration="11219000" />
      <workItem from="1692927557204" duration="37943000" />
      <workItem from="1693193071611" duration="2851000" />
      <workItem from="1693204938579" duration="23131000" />
      <workItem from="1693363930401" duration="42476000" />
      <workItem from="1693531822358" duration="25683000" />
      <workItem from="1693563256738" duration="2076000" />
      <workItem from="1693791043956" duration="27398000" />
      <workItem from="1693877342025" duration="32887000" />
      <workItem from="1693967725704" duration="12654000" />
      <workItem from="1694412227135" duration="59633000" />
      <workItem from="1694570271956" duration="65073000" />
      <workItem from="1695006106086" duration="6728000" />
      <workItem from="1695021305259" duration="6551000" />
      <workItem from="1695030600489" duration="56537000" />
      <workItem from="1695259598120" duration="20123000" />
      <workItem from="1695606362120" duration="5363000" />
      <workItem from="1696643313359" duration="10001000" />
      <workItem from="1696728817687" duration="6585000" />
      <workItem from="1696844994957" duration="15713000" />
      <workItem from="1697010115598" duration="3185000" />
      <workItem from="1697077234416" duration="2912000" />
      <workItem from="1697105028049" duration="6819000" />
      <workItem from="1697117854862" duration="12834000" />
      <workItem from="1697161618981" duration="3945000" />
      <workItem from="1697249627807" duration="2520000" />
      <workItem from="1697269419684" duration="2189000" />
      <workItem from="1697276815156" duration="1429000" />
      <workItem from="1697422000171" duration="1656000" />
      <workItem from="1697428411621" duration="4042000" />
      <workItem from="1697513681251" duration="1936000" />
      <workItem from="1697596668316" duration="3650000" />
      <workItem from="1697610796422" duration="4962000" />
      <workItem from="1697785587296" duration="2108000" />
      <workItem from="1697872792245" duration="4014000" />
      <workItem from="1697935886947" duration="9841000" />
      <workItem from="1698024904664" duration="879000" />
      <workItem from="1698025800243" duration="33631000" />
      <workItem from="1698129838410" duration="8729000" />
      <workItem from="1698220865541" duration="7891000" />
      <workItem from="1698286575056" duration="8728000" />
      <workItem from="1698378025891" duration="331000" />
      <workItem from="1698398690883" duration="604000" />
      <workItem from="1698632408815" duration="16186000" />
      <workItem from="1698732902639" duration="12571000" />
      <workItem from="1698803320338" duration="32289000" />
      <workItem from="1698974845805" duration="10423000" />
      <workItem from="1699235298793" duration="14973000" />
      <workItem from="1699263467328" duration="3511000" />
      <workItem from="1699320358307" duration="17636000" />
      <workItem from="1699407219822" duration="1102000" />
      <workItem from="1699409535135" duration="3462000" />
      <workItem from="1699518993022" duration="3795000" />
      <workItem from="1699523146169" duration="2209000" />
      <workItem from="1699579740420" duration="5754000" />
      <workItem from="1699597124151" duration="114000" />
      <workItem from="1699597250781" duration="2346000" />
      <workItem from="1699602070443" duration="7533000" />
      <workItem from="1699671534640" duration="8303000" />
      <workItem from="1699847600191" duration="15088000" />
      <workItem from="1699925627030" duration="15775000" />
      <workItem from="1699950655402" duration="5803000" />
      <workItem from="1700012115482" duration="64523000" />
      <workItem from="1700196020029" duration="33951000" />
      <workItem from="1700444086546" duration="4795000" />
      <workItem from="1700450809983" duration="29170000" />
      <workItem from="1700542967654" duration="86015000" />
      <workItem from="1700789030008" duration="60046000" />
      <workItem from="1701252124312" duration="41590000" />
      <workItem from="1701395314704" duration="5114000" />
      <workItem from="1701408611047" duration="25682000" />
      <workItem from="1701744330225" duration="107733000" />
      <workItem from="1701999099778" duration="4536000" />
      <workItem from="1702003653137" duration="45940000" />
      <workItem from="1702370327410" duration="2350000" />
      <workItem from="1702433295995" duration="8968000" />
      <workItem from="1702534559114" duration="1284000" />
      <workItem from="1702541142144" duration="4360000" />
      <workItem from="1702624804186" duration="225000" />
      <workItem from="1702627509205" duration="1012000" />
      <workItem from="1702690864054" duration="9547000" />
      <workItem from="1702862852306" duration="716000" />
      <workItem from="1702863797397" duration="27539000" />
      <workItem from="1703035902321" duration="391000" />
      <workItem from="1703036460595" duration="24198000" />
      <workItem from="1703471051580" duration="3449000" />
      <workItem from="1703557738133" duration="10482000" />
      <workItem from="1703657284402" duration="12832000" />
      <workItem from="1704086055552" duration="1164000" />
      <workItem from="1704160051727" duration="27567000" />
      <workItem from="1704332787249" duration="16053000" />
      <workItem from="1704418438986" duration="13486000" />
      <workItem from="1704504874556" duration="6954000" />
      <workItem from="1704677421267" duration="14212000" />
      <workItem from="1704869239497" duration="25911000" />
      <workItem from="1705039930871" duration="19912000" />
      <workItem from="1705282395682" duration="17272000" />
      <workItem from="1705370399986" duration="23704000" />
      <workItem from="1705455512349" duration="20441000" />
      <workItem from="1705541720882" duration="22127000" />
      <workItem from="1705633154382" duration="1532000" />
      <workItem from="1705895822686" duration="10320000" />
      <workItem from="1705981897853" duration="9495000" />
      <workItem from="1706234536543" duration="15361000" />
      <workItem from="1706319325923" duration="12612000" />
      <workItem from="1706492833128" duration="14771000" />
      <workItem from="1706601884973" duration="1207000" />
      <workItem from="1706683856970" duration="11996000" />
      <workItem from="1706754486918" duration="13224000" />
      <workItem from="1706839823774" duration="20811000" />
      <workItem from="1707010226725" duration="23490000" />
      <workItem from="1707097055146" duration="14454000" />
      <workItem from="1707183605979" duration="11607000" />
      <workItem from="1707269616576" duration="2516000" />
      <workItem from="1707360391861" duration="976000" />
      <workItem from="1708307107636" duration="23854000" />
      <workItem from="1708417035454" duration="669000" />
      <workItem from="1708480865282" duration="20526000" />
      <workItem from="1708570891954" duration="13163000" />
      <workItem from="1708659645648" duration="8005000" />
      <workItem from="1708911624949" duration="7898000" />
      <workItem from="1709020599320" duration="1169000" />
      <workItem from="1709085595008" duration="2629000" />
      <workItem from="1709089567883" duration="1461000" />
      <workItem from="1709110783366" duration="1520000" />
      <workItem from="1709176481741" duration="22546000" />
      <workItem from="1709286411563" duration="179000" />
      <workItem from="1709287710380" duration="1079000" />
      <workItem from="1709518655973" duration="16224000" />
      <workItem from="1709603798504" duration="3760000" />
      <workItem from="1709630041383" duration="808000" />
      <workItem from="1709705485911" duration="6233000" />
      <workItem from="1709716077998" duration="2773000" />
      <workItem from="1709776432188" duration="407000" />
      <workItem from="1709777132001" duration="390000" />
      <workItem from="1709777613627" duration="21561000" />
      <workItem from="1709869438240" duration="8081000" />
      <workItem from="1709952583811" duration="14898000" />
      <workItem from="1710120901381" duration="48721000" />
      <workItem from="1710380252089" duration="5444000" />
      <workItem from="1710410815493" duration="316000" />
      <workItem from="1710482433585" duration="4397000" />
      <workItem from="1710487852021" duration="1130000" />
      <workItem from="1710725532395" duration="35010000" />
      <workItem from="1710898952284" duration="7281000" />
      <workItem from="1710991984619" duration="20910000" />
      <workItem from="1711331571865" duration="3919000" />
      <workItem from="1711422370161" duration="7400000" />
      <workItem from="1711618484056" duration="2546000" />
      <workItem from="1711700722770" duration="2323000" />
      <workItem from="1711935764579" duration="14467000" />
      <workItem from="1712112239486" duration="7005000" />
      <workItem from="1712459250920" duration="24100000" />
      <workItem from="1712713136901" duration="11313000" />
      <workItem from="1712799661656" duration="72458000" />
      <workItem from="1713344860623" duration="3077000" />
      <workItem from="1713347969319" duration="21214000" />
      <workItem from="1713750315725" duration="26959000" />
      <workItem from="1713924804998" duration="5313000" />
      <workItem from="1714025185370" duration="322000" />
      <workItem from="1714035574201" duration="990000" />
      <workItem from="1714120263398" duration="2206000" />
      <workItem from="1714122890894" duration="860000" />
      <workItem from="1714295877175" duration="1191000" />
      <workItem from="1714355180103" duration="11333000" />
      <workItem from="1714449513712" duration="24019000" />
      <workItem from="1724488935342" duration="516000" />
      <workItem from="1724636773421" duration="4877000" />
      <workItem from="1724661525727" duration="2223000" />
      <workItem from="1724726184231" duration="6523000" />
      <workItem from="1724747688843" duration="1104000" />
      <workItem from="1724748830529" duration="3000000" />
      <workItem from="1724825398937" duration="4827000" />
      <workItem from="1724924471893" duration="506000" />
      <workItem from="1724981876162" duration="3800000" />
      <workItem from="1724999072434" duration="87000" />
      <workItem from="1725241993800" duration="7531000" />
      <workItem from="1725269420540" duration="44000" />
      <workItem from="1725328276646" duration="23324000" />
      <workItem from="1725417332246" duration="3092000" />
      <workItem from="1725434616546" duration="66000" />
      <workItem from="1725592065760" duration="240000" />
      <workItem from="1725602778299" duration="3505000" />
      <workItem from="1725611556281" duration="704000" />
      <workItem from="1725699989292" duration="2177000" />
      <workItem from="1725939352067" duration="3808000" />
      <workItem from="1725964929777" duration="15518000" />
      <workItem from="1726105305953" duration="2856000" />
      <workItem from="1726109155312" duration="1412000" />
      <workItem from="1726113248362" duration="5051000" />
      <workItem from="1726127920300" duration="8111000" />
      <workItem from="1726191283288" duration="19218000" />
      <workItem from="1726640401208" duration="130000" />
      <workItem from="1726650217902" duration="402000" />
      <workItem from="1726734464959" duration="4409000" />
      <workItem from="1726742796328" duration="9816000" />
      <workItem from="1726815269284" duration="9154000" />
      <workItem from="1726826601449" duration="43000" />
      <workItem from="1726888367374" duration="15704000" />
      <workItem from="1727060224221" duration="135000" />
      <workItem from="1727060499065" duration="1012000" />
      <workItem from="1727077495399" duration="3006000" />
      <workItem from="1727233965693" duration="267000" />
      <workItem from="1727248131490" duration="85000" />
      <workItem from="1727404942204" duration="5203000" />
      <workItem from="1727575491592" duration="1173000" />
      <workItem from="1727592745982" duration="2257000" />
      <workItem from="1727687751561" duration="173000" />
      <workItem from="1727688141786" duration="254000" />
      <workItem from="1728050618427" duration="1208000" />
      <workItem from="1728351744531" duration="20735000" />
      <workItem from="1728462608081" duration="2700000" />
      <workItem from="1728524416357" duration="9840000" />
      <workItem from="1728610897885" duration="2964000" />
      <workItem from="1728628269927" duration="794000" />
      <workItem from="1728631677294" duration="1983000" />
      <workItem from="1728637003435" duration="1085000" />
      <workItem from="1728638222098" duration="228000" />
      <workItem from="1728639432103" duration="361000" />
      <workItem from="1728640449022" duration="29000" />
      <workItem from="1728640516651" duration="70000" />
      <workItem from="1728640615974" duration="57000" />
      <workItem from="1728698443074" duration="403000" />
      <workItem from="1728713169244" duration="763000" />
      <workItem from="1728721800965" duration="1045000" />
      <workItem from="1728895520654" duration="390000" />
      <workItem from="1728896259289" duration="20000" />
      <workItem from="1728899582822" duration="8207000" />
      <workItem from="1728967373981" duration="5295000" />
      <workItem from="1729065153716" duration="799000" />
      <workItem from="1729147758380" duration="8085000" />
      <workItem from="1729216080397" duration="3012000" />
      <workItem from="1729235178650" duration="1967000" />
      <workItem from="1729303274958" duration="966000" />
      <workItem from="1729324638369" duration="8559000" />
      <workItem from="1729498856463" duration="1791000" />
      <workItem from="1729578253519" duration="11918000" />
      <workItem from="1729672709376" duration="15155000" />
      <workItem from="1729820653779" duration="2549000" />
      <workItem from="1729828642347" duration="11668000" />
      <workItem from="1730079572088" duration="18861000" />
      <workItem from="1730190651949" duration="1039000" />
      <workItem from="1730192770215" duration="13842000" />
      <workItem from="1730273379479" duration="195000" />
      <workItem from="1730278361743" duration="9053000" />
      <workItem from="1730363478030" duration="201000" />
      <workItem from="1730368818835" duration="9502000" />
      <workItem from="1730453329366" duration="1377000" />
      <workItem from="1730690370810" duration="21788000" />
      <workItem from="1730800223192" duration="804000" />
      <workItem from="1730878795765" duration="2928000" />
      <workItem from="1730959230328" duration="9278000" />
      <workItem from="1730973524961" duration="880000" />
      <workItem from="1730974867897" duration="11000" />
      <workItem from="1731031018305" duration="15651000" />
      <workItem from="1731056800843" duration="1591000" />
      <workItem from="1731289297080" duration="298000" />
      <workItem from="1731289802891" duration="2796000" />
      <workItem from="1731314519154" duration="284000" />
      <workItem from="1731314992090" duration="6489000" />
      <workItem from="1731400782640" duration="130000" />
      <workItem from="1731407552458" duration="676000" />
      <workItem from="1731549687868" duration="1382000" />
      <workItem from="1731635030073" duration="4123000" />
      <workItem from="1731651035971" duration="863000" />
      <workItem from="1731653269758" duration="1264000" />
      <workItem from="1731659352442" duration="19000" />
      <workItem from="1732002975833" duration="6004000" />
      <workItem from="1732069558627" duration="704000" />
      <workItem from="1732086784360" duration="1887000" />
      <workItem from="1732096466269" duration="18000" />
      <workItem from="1732098003851" duration="30000" />
      <workItem from="1732155009992" duration="6601000" />
      <workItem from="1732169860755" duration="4431000" />
      <workItem from="1732239619714" duration="9306000" />
      <workItem from="1732773579802" duration="3725000" />
      <workItem from="1732844174833" duration="4339000" />
      <workItem from="1732864436363" duration="97000" />
      <workItem from="1732876466998" duration="25000" />
      <workItem from="1732934653888" duration="23424000" />
      <workItem from="1733104692190" duration="13170000" />
      <workItem from="1733190274721" duration="4476000" />
      <workItem from="1733206310855" duration="1000" />
      <workItem from="1733206321079" duration="1862000" />
      <workItem from="1733209488841" duration="223000" />
      <workItem from="1733277413142" duration="6083000" />
      <workItem from="1733301345956" duration="2045000" />
      <workItem from="1733304016486" duration="24016000" />
      <workItem from="1733449346991" duration="2859000" />
      <workItem from="1733462376978" duration="13731000" />
      <workItem from="1733708539365" duration="25000" />
      <workItem from="1733800168475" duration="21620000" />
      <workItem from="1733902767186" duration="2778000" />
      <workItem from="1733967258779" duration="29553000" />
    </task>
    <task id="LOCAL-00001" summary="fix:增加短信发送">
      <created>1683860044707</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1683860044707</updated>
    </task>
    <task id="LOCAL-00002" summary="fix:9124也从第二条短信开始读取">
      <created>1685758994407</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1685758994407</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.log" />
    <option featureType="com.intellij.fileTypeFactory" implementationName="requirements.txt" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="6c4ea5be-cca2-402d-a293-ae30f899c0e0">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feat-lj" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:增加短信发送" />
    <MESSAGE value="fix:9124也从第二条短信开始读取" />
    <MESSAGE value="feat:创建项目" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:9124也从第二条短信开始读取" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_yuyi/pipelines.py</url>
          <line>44</line>
          <option name="timeStamp" value="190" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_yuyi/pipelines.py</url>
          <line>47</line>
          <option name="timeStamp" value="193" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_yuyi/pipelines.py</url>
          <line>86</line>
          <option name="timeStamp" value="195" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wdt_data/migrations.py</url>
          <line>11</line>
          <option name="timeStamp" value="253" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_yuyi/pipelines.py</url>
          <line>90</line>
          <option name="timeStamp" value="254" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/models.py</url>
          <line>1268</line>
          <option name="timeStamp" value="384" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/models.py</url>
          <line>1275</line>
          <option name="timeStamp" value="386" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/boss/settings/config.py</url>
          <line>20</line>
          <option name="timeStamp" value="459" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>189</line>
          <option name="timeStamp" value="841" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>214</line>
          <option name="timeStamp" value="843" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>212</line>
          <option name="timeStamp" value="844" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/models.py</url>
          <line>138</line>
          <option name="timeStamp" value="846" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>267</line>
          <option name="timeStamp" value="883" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wdt_data/inventory_details_put.py</url>
          <line>238</line>
          <option name="timeStamp" value="1004" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/demo.py</url>
          <line>12</line>
          <option name="timeStamp" value="1112" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/price_cat/price_cat/spiders/monitor.py</url>
          <line>127</line>
          <option name="timeStamp" value="1140" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_goods.py</url>
          <line>227</line>
          <option name="timeStamp" value="1180" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/price_cat/price_cat/pipelines.py</url>
          <line>36</line>
          <option name="timeStamp" value="1233" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/price_cat/price_cat/spiders/monitor.py</url>
          <line>73</line>
          <option name="timeStamp" value="1234" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>130</line>
          <option name="timeStamp" value="1274" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>104</line>
          <option name="timeStamp" value="1275" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>117</line>
          <option name="timeStamp" value="1278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/finance_data/cw_put_data.py</url>
          <line>115</line>
          <option name="timeStamp" value="1279" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_goods_detail.py</url>
          <line>63</line>
          <option name="timeStamp" value="1288" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_comment_pic_video.py</url>
          <line>46</line>
          <option name="timeStamp" value="1335" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>402</line>
          <option name="timeStamp" value="1385" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>786</line>
          <option name="timeStamp" value="1431" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/price_cat/price_cat/spiders/monitor.py</url>
          <line>131</line>
          <option name="timeStamp" value="1445" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>210</line>
          <option name="timeStamp" value="1458" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/apis.py</url>
          <line>225</line>
          <option name="timeStamp" value="1461" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>725</line>
          <option name="timeStamp" value="1462" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_grass_articles.py</url>
          <line>72</line>
          <option name="timeStamp" value="1509" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_goods.py</url>
          <line>210</line>
          <option name="timeStamp" value="1510" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>795</line>
          <option name="timeStamp" value="1511" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py</url>
          <line>193</line>
          <option name="timeStamp" value="1529" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>708</line>
          <option name="timeStamp" value="1534" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/models.py</url>
          <line>1564</line>
          <option name="timeStamp" value="1536" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/shadow_query_record.py</url>
          <line>55</line>
          <option name="timeStamp" value="1547" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/shadow_query_record.py</url>
          <line>87</line>
          <option name="timeStamp" value="1552" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sku_goods.py</url>
          <line>49</line>
          <option name="timeStamp" value="1601" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/apis.py</url>
          <line>43</line>
          <option name="timeStamp" value="1604" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_goods_into.py</url>
          <line>492</line>
          <option name="timeStamp" value="1708" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_key_search.py</url>
          <line>179</line>
          <option name="timeStamp" value="1720" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle.py</url>
          <line>114</line>
          <option name="timeStamp" value="1734" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle.py</url>
          <line>203</line>
          <option name="timeStamp" value="1736" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle.py</url>
          <option name="timeStamp" value="1737" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak.py</url>
          <line>152</line>
          <option name="timeStamp" value="1746" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak.py</url>
          <line>135</line>
          <option name="timeStamp" value="1776" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/moojing/apis.py</url>
          <line>228</line>
          <option name="timeStamp" value="1789" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_grass_articles.py</url>
          <line>75</line>
          <option name="timeStamp" value="1813" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_sycm_key_into.py</url>
          <line>70</line>
          <option name="timeStamp" value="1872" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_voc_comment.py</url>
          <line>97</line>
          <option name="timeStamp" value="1925" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_voc_comment.py</url>
          <line>164</line>
          <option name="timeStamp" value="1926" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_model.py</url>
          <line>147</line>
          <option name="timeStamp" value="1927" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_voc_comment.py</url>
          <line>162</line>
          <option name="timeStamp" value="1928" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_model.py</url>
          <line>144</line>
          <option name="timeStamp" value="1929" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tm_comment/tm_login.py</url>
          <line>73</line>
          <option name="timeStamp" value="1930" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tm_comment/main_deail_put_into.py</url>
          <line>48</line>
          <option name="timeStamp" value="1932" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tm_comment/main_deail_put_into.py</url>
          <line>55</line>
          <option name="timeStamp" value="1937" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_competition_loss.py</url>
          <line>99</line>
          <option name="timeStamp" value="1954" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_competition_loss.py</url>
          <line>268</line>
          <option name="timeStamp" value="1955" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_competition_loss.py</url>
          <line>145</line>
          <option name="timeStamp" value="1956" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/apis.py</url>
          <line>50</line>
          <option name="timeStamp" value="1975" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama_cy/apis.py</url>
          <line>62</line>
          <option name="timeStamp" value="1990" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/demo.py</url>
          <line>62</line>
          <option name="timeStamp" value="2001" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/download_video.py</url>
          <line>74</line>
          <option name="timeStamp" value="2011" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/download_video.py</url>
          <line>48</line>
          <option name="timeStamp" value="2013" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/dy_video.py</url>
          <line>74</line>
          <option name="timeStamp" value="2016" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_chanqq_info.py</url>
          <line>176</line>
          <option name="timeStamp" value="2028" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/models.py</url>
          <line>850</line>
          <option name="timeStamp" value="2034" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/models.py</url>
          <line>555</line>
          <option name="timeStamp" value="2035" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/moojing/mojin_login.py</url>
          <line>91</line>
          <option name="timeStamp" value="2037" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/moojing/mojin_login.py</url>
          <line>119</line>
          <option name="timeStamp" value="2039" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/intelligence/apis.py</url>
          <line>106</line>
          <option name="timeStamp" value="2050" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py</url>
          <line>284</line>
          <option name="timeStamp" value="2052" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak01.py</url>
          <line>122</line>
          <option name="timeStamp" value="2055" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_goods_optimize_key.py</url>
          <line>68</line>
          <option name="timeStamp" value="2057" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_goods_optimize_key.py</url>
          <line>95</line>
          <option name="timeStamp" value="2058" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_goods_optimize_key.py</url>
          <line>75</line>
          <option name="timeStamp" value="2061" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak.py</url>
          <line>128</line>
          <option name="timeStamp" value="2062" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak.py</url>
          <line>110</line>
          <option name="timeStamp" value="2065" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/demo1.py</url>
          <line>18</line>
          <option name="timeStamp" value="2067" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/demo1.py</url>
          <line>31</line>
          <option name="timeStamp" value="2068" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/demo1.py</url>
          <line>47</line>
          <option name="timeStamp" value="2069" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/demo1.py</url>
          <line>49</line>
          <option name="timeStamp" value="2070" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/demo1.py</url>
          <line>40</line>
          <option name="timeStamp" value="2071" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak01.py</url>
          <line>106</line>
          <option name="timeStamp" value="2073" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/comment.py</url>
          <line>152</line>
          <option name="timeStamp" value="2083" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/comment.py</url>
          <line>108</line>
          <option name="timeStamp" value="2086" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_apis/apis.py</url>
          <line>169</line>
          <option name="timeStamp" value="2087" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/comment.py</url>
          <line>215</line>
          <option name="timeStamp" value="2091" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/comment.py</url>
          <line>90</line>
          <option name="timeStamp" value="2092" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_apis/apis.py</url>
          <line>163</line>
          <option name="timeStamp" value="2094" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_key_search.py</url>
          <line>100</line>
          <option name="timeStamp" value="2097" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_grass_articles.py</url>
          <line>63</line>
          <option name="timeStamp" value="2098" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_apis/apis.py</url>
          <line>307</line>
          <option name="timeStamp" value="2099" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/intelligence/apis.py</url>
          <line>89</line>
          <option name="timeStamp" value="2104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/comment_handle_bak01.py</url>
          <line>278</line>
          <option name="timeStamp" value="2110" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/message_auth/activity_check.py</url>
          <line>43</line>
          <option name="timeStamp" value="2111" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_goods_note_detail.py</url>
          <line>87</line>
          <option name="timeStamp" value="2112" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/moojing_good_data.py</url>
          <line>448</line>
          <option name="timeStamp" value="2113" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/main_deail_put_into.py</url>
          <line>40</line>
          <option name="timeStamp" value="2114" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jd_comment/main_deail_put_into.py</url>
          <line>47</line>
          <option name="timeStamp" value="2116" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_apis_new/test_voc_new.py</url>
          <line>127</line>
          <option name="timeStamp" value="2118" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/voc_apis_new/test_voc_new.py</url>
          <line>143</line>
          <option name="timeStamp" value="2119" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/voc_yuyi/new_voc_dialog.py</url>
          <line>120</line>
          <option name="timeStamp" value="2133" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py</url>
          <line>316</line>
          <option name="timeStamp" value="2136" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py</url>
          <line>263</line>
          <option name="timeStamp" value="2137" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_goods.py</url>
          <line>61</line>
          <option name="timeStamp" value="2140" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>737</line>
          <option name="timeStamp" value="2147" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_goods_source.py</url>
          <line>29</line>
          <option name="timeStamp" value="2157" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_apis/apis.py</url>
          <line>410</line>
          <option name="timeStamp" value="2160" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/xhs_data/xhs_apis/apis.py</url>
          <line>431</line>
          <option name="timeStamp" value="2161" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/intelligence/apis.py</url>
          <line>194</line>
          <option name="timeStamp" value="2164" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ali_code_review/ali_bill_details_put.py</url>
          <line>23</line>
          <option name="timeStamp" value="2169" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/chanmama/test_chanmama.py</url>
          <line>334</line>
          <option name="timeStamp" value="2178" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>667</line>
          <option name="timeStamp" value="2179" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>678</line>
          <option name="timeStamp" value="2181" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>674</line>
          <option name="timeStamp" value="2182" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>310</line>
          <option name="timeStamp" value="2241" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wdt_data/inventory_details_put.py</url>
          <line>247</line>
          <option name="timeStamp" value="2244" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm_comparison_goods.py</url>
          <line>418</line>
          <option name="timeStamp" value="2255" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>291</line>
          <option name="timeStamp" value="2256" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>164</line>
          <option name="timeStamp" value="2258" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/dy_shop_goods_put.py</url>
          <line>62</line>
          <option name="timeStamp" value="2260" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/tm_shop_goods_put.py</url>
          <line>32</line>
          <option name="timeStamp" value="2261" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/brand_goods_monitor.py</url>
          <line>114</line>
          <option name="timeStamp" value="2262" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/qc_api/test_qc.py</url>
          <line>54</line>
          <option name="timeStamp" value="2268" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/shadow_data/qc_api/qc_apis.py</url>
          <line>56</line>
          <option name="timeStamp" value="2270" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/moojing/test_moojing.py</url>
          <line>46</line>
          <option name="timeStamp" value="2272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong.py</url>
          <line>233</line>
          <option name="timeStamp" value="2275" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/sycm_data/sycm/apis.py</url>
          <line>55</line>
          <option name="timeStamp" value="2278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_handle.py</url>
          <line>721</line>
          <option name="timeStamp" value="2280" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong.py</url>
          <line>497</line>
          <option name="timeStamp" value="2281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py</url>
          <line>176</line>
          <option name="timeStamp" value="2283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py</url>
          <line>345</line>
          <option name="timeStamp" value="2290" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/brand_data/brand_qingbaotong_into.py</url>
          <line>139</line>
          <option name="timeStamp" value="2296" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="spu_name_list" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/das_spider$tm_comment_put.coverage" NAME="tm_comment_put 覆盖结果" MODIFIED="1732006432646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data/comment" />
    <SUITE FILE_PATH="coverage/das_spider$brand_chanqq_info.coverage" NAME="brand_chanqq_info 覆盖结果" MODIFIED="1738918388792" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$dy_shop_goods_put.coverage" NAME="dy_shop_goods_put 覆盖结果" MODIFIED="1745981341815" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$brand_sku_goods.coverage" NAME="brand_sku_goods Coverage Results" MODIFIED="1714122303452" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$apis_new.coverage" NAME="apis_new 覆盖结果" MODIFIED="1712816128914" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_apis_new" />
    <SUITE FILE_PATH="coverage/das_spider$download_video.coverage" NAME="download_video 覆盖结果" MODIFIED="1742265177604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$sycm_comparison_goods.coverage" NAME="sycm_comparison_goods 覆盖结果" MODIFIED="1751860641076" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$new_voc_comment__1_.coverage" NAME="new_voc_comment (1) 覆盖结果" MODIFIED="1712909909379" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$Unittests_for_test_chanmama_TestCases_test_get_v5_brand_detail_products.coverage" NAME="Unittests for test_chanmama.TestCases.test_get_v5_brand_detail_products Coverage Results" MODIFIED="1714381420994" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data/chanmama" />
    <SUITE FILE_PATH="coverage/das_spider$migrations.coverage" NAME="migrations 覆盖结果" MODIFIED="1701877818892" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wdt_data" />
    <SUITE FILE_PATH="coverage/das_spider$demo1.coverage" NAME="demo1 覆盖结果" MODIFIED="1734158874948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$apis__1_.coverage" NAME="apis (1) 覆盖结果" MODIFIED="1737537574796" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data/sycm" />
    <SUITE FILE_PATH="coverage/das_spider$xhs_put_cost.coverage" NAME="xhs_put_cost 覆盖结果" MODIFIED="1750663952773" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$activity_check.coverage" NAME="activity_check 覆盖结果" MODIFIED="1737767161981" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/message_auth" />
    <SUITE FILE_PATH="coverage/das_spider$new_voc_dialog.coverage" NAME="new_voc_dialog 覆盖结果" MODIFIED="1742454327927" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$middlewares.coverage" NAME="middlewares 覆盖结果" MODIFIED="1690957927802" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$apis.coverage" NAME="apis 覆盖结果" MODIFIED="1744796527097" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data/sycm" />
    <SUITE FILE_PATH="coverage/das_spider$brand_qingbaotong_into.coverage" NAME="brand_qingbaotong_into 覆盖结果" MODIFIED="1751957793183" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$xhs_key_search.coverage" NAME="xhs_key_search 覆盖结果" MODIFIED="1736129164457" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$job_info.coverage" NAME="job_info 覆盖结果" MODIFIED="1688700070043" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boss/work_desc" />
    <SUITE FILE_PATH="coverage/das_spider$comment_handle_bak.coverage" NAME="comment_handle_bak 覆盖结果" MODIFIED="1737341185394" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$comment.coverage" NAME="comment 覆盖结果" MODIFIED="1735370098004" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jd_comment" />
    <SUITE FILE_PATH="coverage/das_spider$brand_qingbaotong.coverage" NAME="brand_qingbaotong 覆盖结果" MODIFIED="1751863316959" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$main_chanmm_info.coverage" NAME="main_chanmm_info 覆盖结果" MODIFIED="1712541231238" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$main__1_.coverage" NAME="main (1) 覆盖结果" MODIFIED="1738808775468" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/price_cat" />
    <SUITE FILE_PATH="coverage/das_spider$brand_goods_monitor.coverage" NAME="brand_goods_monitor 覆盖结果" MODIFIED="1746844298190" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$encryption.coverage" NAME="encryption 覆盖结果" MODIFIED="1750643574188" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/das_spider$main_moojing.coverage" NAME="main_moojing 覆盖结果" MODIFIED="1702710057297" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$sycm_competition_loss.coverage" NAME="sycm_competition_loss 覆盖结果" MODIFIED="1744078333866" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$set_file_name.coverage" NAME="set_file_name 覆盖结果" MODIFIED="1691410117764" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi/utils" />
    <SUITE FILE_PATH="coverage/das_spider$sycm_goods_source.coverage" NAME="sycm_goods_source 覆盖结果" MODIFIED="1747624815852" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$__init__.coverage" NAME="__init__ 覆盖结果" MODIFIED="1735373557168" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jd_comment" />
    <SUITE FILE_PATH="coverage/das_spider$filename.coverage" NAME="set_file_name 覆盖结果" MODIFIED="1691050716960" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi/utils" />
    <SUITE FILE_PATH="coverage/das_spider$xhs_goods_detail.coverage" NAME="xhs_goods_detail 覆盖结果" MODIFIED="1750223355696" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$read_code.coverage" NAME="read_code 覆盖结果" MODIFIED="1709262978957" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/das_spider$qc_expense_reduce_record.coverage" NAME="qc_expense_reduce_record 覆盖结果" MODIFIED="1747735736135" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$cy_video.coverage" NAME="cy_video 覆盖结果" MODIFIED="1737528843805" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$main_new.coverage" NAME="main_new 覆盖结果" MODIFIED="1741243242948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$moojing_good_data.coverage" NAME="moojing_good_data 覆盖结果" MODIFIED="1741604493925" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$main.coverage" NAME="main 覆盖结果" MODIFIED="1751964522156" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$voc_comment_pic_video.coverage" NAME="voc_comment_pic_video 覆盖结果" MODIFIED="1701409006273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$comment_handle.coverage" NAME="comment_handle 覆盖结果" MODIFIED="1705545472909" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$pipelines.coverage" NAME="pipelines 覆盖结果" MODIFIED="1691544678477" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$proxy1.coverage" NAME="proxy1 覆盖结果" MODIFIED="1689330189878" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boss/work_desc" />
    <SUITE FILE_PATH="coverage/das_spider$new_voc_comment.coverage" NAME="new_voc_comment 覆盖结果" MODIFIED="1741243602569" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$brand_sycm_goods_flow_into.coverage" NAME="brand_sycm_goods_flow_into 覆盖结果" MODIFIED="1708499036532" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$monitor_status.coverage" NAME="monitor_status 覆盖结果" MODIFIED="1685512792370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/message_auth" />
    <SUITE FILE_PATH="coverage/das_spider$dingtalk.coverage" NAME="dingtalk 覆盖结果" MODIFIED="1736839747204" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/das_spider$11111.coverage" NAME="11111 覆盖结果" MODIFIED="1726212100324" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$set_file_name__1_.coverage" NAME="set_file_name (1) 覆盖结果" MODIFIED="1691051085050" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi/utils" />
    <SUITE FILE_PATH="coverage/das_spider$demo.coverage" NAME="demo 覆盖结果" MODIFIED="1750043407142" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data/chanmama_cy" />
    <SUITE FILE_PATH="coverage/das_spider$inventory_details_put.coverage" NAME="inventory_details_put 覆盖结果" MODIFIED="1750910391308" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wdt_data" />
    <SUITE FILE_PATH="coverage/das_spider$brand_handle.coverage" NAME="brand_handle 覆盖结果" MODIFIED="1744354612404" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$main_deail_put_into.coverage" NAME="main_deail_put_into 覆盖结果" MODIFIED="1741594978567" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tm_comment" />
    <SUITE FILE_PATH="coverage/das_spider$xhs_grass_articles.coverage" NAME="xhs_grass_articles 覆盖结果" MODIFIED="1750413955186" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$proxy_settings.coverage" NAME="proxy_settings 覆盖结果" MODIFIED="1689225790971" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boss/work_desc" />
    <SUITE FILE_PATH="coverage/das_spider$voc_comment_pic.coverage" NAME="voc_comment_pic 覆盖结果" MODIFIED="1699345587619" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$login.coverage" NAME="login 覆盖结果" MODIFIED="1747304950947" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data/intelligence" />
    <SUITE FILE_PATH="coverage/das_spider$demo_sycm.coverage" NAME="demo_sycm 覆盖结果" MODIFIED="1698742913364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$models.coverage" NAME="models 覆盖结果" MODIFIED="1705547051214" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$database.coverage" NAME="database 覆盖结果" MODIFIED="1700450678657" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$.coverage" NAME=" 覆盖结果" MODIFIED="1747796284831" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data/moojing" />
    <SUITE FILE_PATH="coverage/das_spider$1.coverage" NAME="1 覆盖结果" MODIFIED="1750415971930" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data/xhs_apis" />
    <SUITE FILE_PATH="coverage/das_spider$demo_qq.coverage" NAME="demo_qq 覆盖结果" MODIFIED="1709706487203" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/das_spider$migrations__1_.coverage" NAME="migrations (1) 覆盖结果" MODIFIED="1693533341002" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/moojing_data/database" />
    <SUITE FILE_PATH="coverage/das_spider$base_func.coverage" NAME="base_func 覆盖结果" MODIFIED="1701078681939" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/das_spider$qc_apis.coverage" NAME="qc_apis 覆盖结果" MODIFIED="1735290853572" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data/qc_api" />
    <SUITE FILE_PATH="coverage/das_spider$brand_goods_into.coverage" NAME="brand_goods_into Coverage Results" MODIFIED="1714443741341" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$12343.coverage" NAME="12343 覆盖结果" MODIFIED="1696659060625" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$ali_code_file_put.coverage" NAME="ali_code_file_put 覆盖结果" MODIFIED="1692156806918" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ali_code_review" />
    <SUITE FILE_PATH="coverage/das_spider$get_time.coverage" NAME="get_time 覆盖结果" MODIFIED="1706868316328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data/utils" />
    <SUITE FILE_PATH="coverage/das_spider$encryption__1_.coverage" NAME="encryption (1) 覆盖结果" MODIFIED="1691638540503" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/das_spider$adb_service.coverage" NAME="adb_service 覆盖结果" MODIFIED="1683277600007" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/message_auth" />
    <SUITE FILE_PATH="coverage/das_spider$cy_demo.coverage" NAME="cy_demo 覆盖结果" MODIFIED="1751858708278" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$tm_shop_goods_put.coverage" NAME="tm_shop_goods_put 覆盖结果" MODIFIED="1746519332064" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$voc_comment.coverage" NAME="voc_comment 覆盖结果" MODIFIED="1703037541427" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$voc_comment_new.coverage" NAME="new_voc_comment 覆盖结果" MODIFIED="1713152930321" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi" />
    <SUITE FILE_PATH="coverage/das_spider$main_xhs_search.coverage" NAME="main_xhs_search 覆盖结果" MODIFIED="1699945530283" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$dy_video.coverage" NAME="dy_video 覆盖结果" MODIFIED="1734920275760" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$demo_dd.coverage" NAME="demo_dd 覆盖结果" MODIFIED="1705460373767" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$xhs_goods_note_detail.coverage" NAME="xhs_goods_note_detail 覆盖结果" MODIFIED="1738810841024" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$tm_login.coverage" NAME="tm_login 覆盖结果" MODIFIED="1725330473750" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tm_comment" />
    <SUITE FILE_PATH="coverage/das_spider$_apiDispatch.coverage" NAME="参考案例下载-apiDispatch 覆盖结果" MODIFIED="1692957611741" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/Desktop" />
    <SUITE FILE_PATH="coverage/das_spider$demoqq.coverage" NAME="demoqq 覆盖结果" MODIFIED="1743582938100" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/das_spider$mojin_login.coverage" NAME="mojin_login 覆盖结果" MODIFIED="1747802443529" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data/moojing" />
    <SUITE FILE_PATH="coverage/das_spider$main_xhs_grass_articles.coverage" NAME="main_xhs_grass_articles 覆盖结果" MODIFIED="1735743629201" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data" />
    <SUITE FILE_PATH="coverage/das_spider$comment_handle__1_.coverage" NAME="comment_handle (1) 覆盖结果" MODIFIED="1694141766267" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/moojing_data" />
    <SUITE FILE_PATH="coverage/das_spider$sycm_goods_optimize_key.coverage" NAME="sycm_goods_optimize_key 覆盖结果" MODIFIED="1743387721060" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$week_main.coverage" NAME="week_main 覆盖结果" MODIFIED="1750988882968" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$ali_bill_details_put.coverage" NAME="ali_bill_details_put 覆盖结果" MODIFIED="1744255239187" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ali_code_review" />
    <SUITE FILE_PATH="coverage/das_spider$yuyi.coverage" NAME="yuyi 覆盖结果" MODIFIED="1691720904867" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/voc_yuyi/voc_yuyi/spiders" />
    <SUITE FILE_PATH="coverage/das_spider$monitor.coverage" NAME="monitor 覆盖结果" MODIFIED="1691648033499" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/price_cat/price_cat/spiders" />
    <SUITE FILE_PATH="coverage/das_spider$demo_qqq.coverage" NAME="demo_qqq 覆盖结果" MODIFIED="1707098732620" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$comment_handle_bak01.coverage" NAME="comment_handle_bak01 覆盖结果" MODIFIED="1747803911191" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$main__2_.coverage" NAME="main (2) 覆盖结果" MODIFIED="1689328746266" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boss/work_desc" />
    <SUITE FILE_PATH="coverage/das_spider$shadow_main.coverage" NAME="shadow_main 覆盖结果" MODIFIED="1692774936736" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$111.coverage" NAME="111 覆盖结果" MODIFIED="1750665323825" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data/xhs_apis" />
    <SUITE FILE_PATH="coverage/das_spider$brand_goods.coverage" NAME="brand_goods 覆盖结果" MODIFIED="1751960708373" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$qc_customer_record.coverage" NAME="qc_customer_record 覆盖结果" MODIFIED="1747735461116" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$demo__1_.coverage" NAME="demo (1) 覆盖结果" MODIFIED="1750223342918" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/xhs_data/xhs_apis" />
    <SUITE FILE_PATH="coverage/das_spider$cw_put_data.coverage" NAME="cw_put_data 覆盖结果" MODIFIED="1725246706875" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/finance_data" />
    <SUITE FILE_PATH="coverage/das_spider$sycm_goods_rank.coverage" NAME="sycm_goods_rank 覆盖结果" MODIFIED="1747624617233" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/sycm_data" />
    <SUITE FILE_PATH="coverage/das_spider$ssl.coverage" NAME="ssl 覆盖结果" MODIFIED="1701844070651" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/AppData/Local/Programs/Python/Python39/Lib" />
    <SUITE FILE_PATH="coverage/das_spider$main_chanqq_info.coverage" NAME="main_chanqq_info 覆盖结果" MODIFIED="1705567496959" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$sync_message.coverage" NAME="sync_message 覆盖结果" MODIFIED="1702891237297" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/message_auth" />
    <SUITE FILE_PATH="coverage/das_spider$brand_sycm_key_into.coverage" NAME="brand_sycm_key_into 覆盖结果" MODIFIED="1709021577552" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
    <SUITE FILE_PATH="coverage/das_spider$sync_file_to_oss.coverage" NAME="sync_file_to_oss 覆盖结果" MODIFIED="1684468700424" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/Desktop" />
    <SUITE FILE_PATH="coverage/das_spider$qc_data.coverage" NAME="qc_data 覆盖结果" MODIFIED="1735289270019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/shadow_data" />
    <SUITE FILE_PATH="coverage/das_spider$main_moojing_put_into.coverage" NAME="main_moojing_put_into 覆盖结果" MODIFIED="1741604436424" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/brand_data" />
  </component>
</project>